<template>
  <CRow>
    <CCol col="12" lg="12">
      <CCard no-header>
        <CCardBody>
          <template slot="header"> Coaching </template>

          <c-row>
            <!-- line  -->
            <c-col lg="3" md="3" sm="8">
              <CFormGroup>
                <template #label> Line </template>
                <template #input>
                  <v-select v-model="line_id" :options="userLines" label="name" :value="0" :reduce="(line) => line.line_id"
                    placeholder="Select Line" class="mt-2" @option:selected="getLineBelowEmployees" />
                </template>
              </CFormGroup>
            </c-col>
            <c-col lg="3" md="3" sm="8">
              <CFormGroup>
                <template #label>Employee</template>
                <template #input>
                  <v-select required v-model="employee_id" :options="employees" label="fullname" :value="0"
                    :reduce="(employee) => employee.id" placeholder="Select Employee" class="mt-2"
                    :disabled="!line_id" />
                </template>
              </CFormGroup>
            </c-col>
            <c-col lg="3" md="3" sm="8" v-if="minDate">
              <c-input :min="minDate" label="Date" type="date" placeholder="date" v-model="date"></c-input>
            </c-col>
            <c-col lg="3" md="3" sm="8">
              <CFormGroup>
                <template #label> View </template>
                <template #input>
                  <v-select v-model="view" :options="['All', 'Checked']" label="name" :value="0"
                    placeholder="Select View" class="mt-2" @option:selected="getFilteredCategories" />
                </template>
              </CFormGroup>
            </c-col>
            <template #date="{ item }">
              <td>{{ format_date(item.date) }}</td>
            </template>
          </c-row>

          <c-row>
            <c-col lg="6" md="6" sm="8">
              <h3><strong>Coach Name: {{ authUser.fullname }} </strong></h3>
            </c-col>
            <c-col lg="6" md="6" sm="8">
              <h3><strong>Coach Code: {{ authUser.emp_code }} </strong></h3>
            </c-col>
          </c-row>
        </CCardBody>
        <c-card v-if="line_id != null">
          <c-card-body form v-for="(category, categoryIndex) in categories" :key="categoryIndex + category.id"
            :v-model="selectedAnswers[categoryIndex]">
            <div class="row">
              <c-col lg="4" md="4" sm="12">
                <strong style="color: blue">
                  {{ categoryIndex + 1 }} | {{ "Category: " + category.name }}
                </strong>
                <v-tooltip right>
                  <template v-slot:activator="{ on, attrs }">
                    <v-icon dark v-bind="attrs" v-on="on">mdi-information</v-icon>
                  </template>
                  <span>{{ category.notes }}</span>
                </v-tooltip>
                <strong> {{ totalDegreePerCategory[categoryIndex].degree }} / {{
                  totalDegreePerCategory[categoryIndex].totalCategory
                  }}</strong>
              </c-col>
              <c-col lg="8" md="8" sm="12">
                <input type="checkbox" checked="true" disabled /> <strong style="color: red;">Checkbox For Area Of
                  Improvements
                </strong>
                <CInputRadio checked="true" class="mr-0" disabled /> <span> <strong
                    style="color: red; margin-left:20px ;">Click
                    For
                    Rating
                  </strong></span>
              </c-col>
            </div>
            <div form class="form-group mb-1 p-3" style="background-color: #f5f5f5"
              v-for="(question, questionIndex) in category.questions" :key="questionIndex + question.id"
              :id="`category_${category.id}_question_${question.id}`">
              <c-row>
                <c-col lg="7" md="7" sm="8">
                  <input type="checkbox" v-model="selectedAnswers[categoryIndex].questions[questionIndex].checked" />
                  <strong>{{ questionIndex + 1 }} | {{ question.name }}</strong>
                  <v-tooltip right>
                    <template v-slot:activator="{ on, attrs }">
                      <v-icon dark v-bind="attrs" v-on="on">mdi-information</v-icon>
                    </template>
                    <div style="width: 300px">
                      <span>{{ question.notes }}</span>
                    </div>
                  </v-tooltip>
                </c-col>
                <c-col lg="1" md="1" sm="8">
                </c-col>
                <c-col lg="4" md="4" sm="8">
                  <c-input type="text" placeholder="Add Reason" v-model="selectedAnswers[categoryIndex].questions[questionIndex]
                    .reason" />
                </c-col>
              </c-row>
              <c-row>
                <c-col v-for="(answer, answerIndex) in question.answers" :key="answerIndex + answer.id" class="mr-2"
                  lg="2" md="2" sm="8" style="white-space: nowrap">
                  <CInputRadio class="mr-0" :label="`${answer.name}`"
                    :name="`category_${category.id}_question_${question.id}`"
                    :id="`category_${category.id}_question_${question.id}_answer_${answer.id}`" :value="answer.id"
                    :checked="selectedAnswers[categoryIndex].questions[questionIndex]
                      .answerId === answer.id
                      " @update:checked="
                        updateAnswers(categoryIndex, questionIndex, answer)
                        " custom inline />
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-icon dark v-bind="attrs" v-on="on">mdi-information</v-icon>
                    </template>
                    <span>Weight: {{ answer.weight }} | {{ answer.notes }}</span>
                  </v-tooltip>
                </c-col>
              </c-row>
            </div>
          </c-card-body>
          <c-card-footer>
            <div class="totalCard"><strong> Total Degree: {{ totalDegree }}</strong></div>
          </c-card-footer>
        </c-card>
        <c-card-body>
          <c-row>
            <c-col lg="4" md="4" sm="8">
              <c-textarea label="Strengths" type="text" placeholder="Strengths" v-model="strenghts"></c-textarea>
            </c-col>
            <c-col lg="4" md="4" sm="8">
              <c-textarea label="Area Of Improvement" type="text" placeholder="Area Of Improvement"
                v-model="points_of_improvement"></c-textarea>
            </c-col>
            <c-col lg="4" md="4" sm="8">
              <c-textarea label="Notes" type="text" placeholder="Notes" v-model="notes"></c-textarea>
            </c-col>
          </c-row>
        </c-card-body>
        <CCardFooter>
          <CButton class="text-white" color="primary" @click="store()" style="float: right"
            :disabled="!categories.length">Save
          </CButton>
          <CButton color="danger" class="text-white" @click="goBack" style="float: left">Cancel</CButton>
        </CCardFooter>
      </CCard>
    </CCol>
  </CRow>
</template>

<script>
import MaskedInput from "vue-text-mask";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import { Datetime } from "vue-datetime";
import { mapState } from "vuex";

export default {
  name: "coaching",
  components: {
    MaskedInput,
    vSelect,
    Datetime,
  },
  data: () => {
    return {
      userLines: [],
      view: 'All',
      line_id: null,
      employees: [],
      employee_id: null,
      date: new Date().toISOString().slice(0, 10),
      categories: [],
      minDate: null,
      questions: {},
      selectedAnswers: {},
      score: 0,
      strenghts: "",
      points_of_improvement: "",
      notes: "",
      // catDegree: 0,
      totalDegree: 0,
      totalDegreePerCategory: 0,
    };
  },
  computed: {
    ...mapState("authentication", ["authUser"]),
  },
  methods: {
    getUserLines() {
      return axios.get(`/api/users/${this.authUser.id}/lines/`);
    },
    getDates() {
      return axios.get(`/api/coaching-dates`);

      // axios
      //   .get(`/api/coaching-dates`)
      //   .then((res) => {
      //     this.minDate = res.data.data;
      //   })
      //   .catch((error) => {
      //     this.showErrorMessage(error);
      //   });
    },
    getLineBelowEmployees(value) {
      axios
        .get(`/api/below-users/${this.authUser.id}/lines/${this.line_id}`)
        .then((response) => {
          this.employees = response.data.data;
          this.getCategories();
        })
        .catch((error) => this.showErrorMessage(error));
    },
    getFilteredCategories() {
      if (this.view == 'All') {
        this.getCategories();
      } else {
        axios
          .post(`/api/coaching/${this.line_id}/categories/`, {
            view: this.view,
            employeeId: this.employee_id,
          })
          .then((res) => {
            this.categories = res.data.categories;
            this.totalDegreePerCategory = res.data.totalperCatrgory;
            // this.totalDegree = res.data.total;
            this.selectedAnswers = this.categories.map((category) => {
              return {
                categoryId: category.id,
                questions: category.questions.map((question) => {
                  return {
                    checked: 0,
                    questionId: question.id,
                    answerId: null,
                    answerWeight: null,
                    reason: null,
                  };
                }),
              };
            });
          })
          .catch((error) => {
            this.showErrorMessage(error);
          });
      }
    },
    getCategories() {
      axios
        .get(`/api/coaching/evaluatoruser/${this.line_id}/categories`)
        .then((res) => {
          this.categories = res.data.categories;
          this.totalDegreePerCategory = res.data.totalperCatrgory;
          // this.totalDegree = res.data.total;
          this.selectedAnswers = this.categories.map((category) => {
            return {
              categoryId: category.id,
              questions: category.questions.map((question) => {
                return {
                  checked: 0,
                  questionId: question.id,
                  answerId: null,
                  answerWeight: null,
                  reason: null,
                };
              }),
            };
          });
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    updateAnswers(catIndex, qIndex, answer) {
      this.selectedAnswers[catIndex].questions[qIndex].answerId = answer.id;
      this.selectedAnswers[catIndex].questions[qIndex].answerWeight =
        answer.weight;
      this.totalDegreePerCategory[catIndex].degree += answer.weight;
      this.totalDegree += answer.weight;
    },

    goBack() {
      this.$router.go(-1);
    },
    store() {
      console.log(this.selectedAnswers);
      axios
        .post("/api/coaching/headers", {
          all_questions: this.selectedAnswers,
          results: Object.values(this.selectedAnswers)
            .map((item) => {
              return {
                category_id: item.categoryId,
                questions: item.questions
                  .map((subItem) => {
                    return {
                      checked: subItem.checked ? 1 : 0,
                      question_id: subItem.questionId,
                      answer_id: subItem.answerId,
                      answer_weight: subItem.answerWeight,
                      reason: subItem.reason,
                    };
                  })
                  .filter((item) => item.answer_id !== null),
              };
            })
            .filter((item) => item.questions.length > 0),
          evaluator_id: this.authUser.id,
          employee_id: this.employee_id,
          line_id: this.line_id,
          date: this.date,
          strenghts: this.strenghts,
          points_of_improvement: this.points_of_improvement,
          notes: this.notes,
        })
        .then((response) => {
          this.flash("answers submitted successfully!", "success");
        })
        .catch((error) => this.showErrorMessage(error));
    },
  },
  async created() {
    let [userLinesRes, minDateCoaching] = await Promise.all([
      this.getUserLines(),
      this.getDates(),
    ]);
    this.userLines = userLinesRes.data.data.lines;
    this.minDate = minDateCoaching.data.data;
    
  },
};
</script>
<style scoped>
.theme--dark.v-icon {
  color: unset;
  cursor: pointer;
}

.totalCard {
  width: 300px;
  height: 50px;
  float: right;
  border-radius: 5px;
  color: white;
  padding: 15px;
  background-color: brown;
  animation-name: totalCard;
  animation-duration: 4s;
}
</style>
