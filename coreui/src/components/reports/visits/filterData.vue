<template>
  <c-card>
    <c-card-header>Visits Report</c-card-header>
    <c-card-body>
      <c-tabs>
        <c-tab active>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-chart-pie" /> Main
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-input label="From" type="date" placeholder="From" v-model="from_date"></c-input>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-input label="To" type="date" placeholder="To" v-model="to_date" @input="getAllData"></c-input>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-8">
                  <c-form-group>
                    <template #label> Line </template>
                    <template #input>
                      <input label="All" v-if="lines.length != 0" id="line" class="m-1" type="checkbox"
                        v-model="checkAllLines" title="Check All lines" @change="checkAllLine" />
                      <label for="line" v-if="lines.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="line_id" :options="lines" label="name" :value="0" :reduce="(line) => line.id"
                        placeholder="Select Lines" class="mt-2" multiple @input="getLineData" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label> Division </template>
                    <template #input>
                      <input label="All" id="division" v-if="divisions.length != 0" class="m-1" type="checkbox"
                        v-model="checkAllDivisions" title="Check All Divisions" @change="checkAllDivs" />
                      <label for="division" v-if="divisions.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="division_id" :options="divisions" label="name" :value="0"
                        :reduce="(division) => division.id" placeholder="Select Divisions" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label> Employee </template>
                    <template #input>
                      <input label="All" v-if="users.length != 0" id="user" class="m-1" type="checkbox"
                        v-model="checkAllUsers" title="Check All Users" @change="checkAllEmployees" />
                      <label for="user" v-if="users.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="user_id" :options="users" label="fullname" :value="0"
                        :reduce="(user) => user.id" placeholder="Select Users" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-8">
                  <c-form-group>
                    <template #label> Visits </template>
                    <template #input>
                      <v-select v-model="value" :options="options" label="name" placeholder="Select Visit"
                        :reduce="(option) => option.id" class="mt-3" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-8" v-if="value == 2">
                  <c-form-group>
                    <template #label> Actual </template>
                    <template #input>
                      <v-select v-model="actualDataId" :options="actualData" label="name" placeholder="Select Actual"
                        :reduce="(option) => option.id" class="mt-2" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-8" v-if="value == 2">
                  <c-form-group>
                    <template #label> Inputs </template>
                    <template #input>
                      <v-select v-model="actualProductDataId" :options="actualProductData" label="name"
                        placeholder="Select Input" :reduce="(option) => option.id" class="mt-2" />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-8" v-if="value == 2">
                  <c-form-group>
                    <template #label> Status </template>
                    <template #input>
                      <v-select v-model="status_id" :options="status" label="name" placeholder="Select status"
                        :reduce="(status) => status.id" class="mt-2" />
                    </template>
                  </c-form-group>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
        <c-tab>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-chart-pie" /> Account Types &
            Specialities
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Account Type </template>
                    <template #input>
                      <input label="All" id="type" v-if="types.length != 0" class="m-1" type="checkbox"
                        v-model="allAccountTypes" title="Check All Types" @change="checkAllAccounts" />
                      <label for="type" v-if="types.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="type_id" :options="types" label="name" :value="0" :reduce="(type) => type.id"
                        placeholder="Select Account Type" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8">
                  <c-form-group>
                    <template #label> Speciality </template>
                    <template #input>
                      <input label="All" id="speciality" v-if="specialities.length != 0" class="m-1" type="checkbox"
                        v-model="checkAllSpecialities" title="Check All Specialities" @change="checkAllSpech" />
                      <label for="speciality" v-if="specialities.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="speciality_id" :options="specialities" label="name" :value="0"
                        :reduce="(speciality) => speciality.id" placeholder="Select Speciality" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label> Bricks </template>
                    <template #input>
                      <input label="All" id="brick" v-if="bricks.length != 0" class="m-1" type="checkbox"
                        v-model="checkAllBricks" title="Check All Bricks" @change="checkAllBrick" />
                      <label for="brick" v-if="bricks.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="brick_id" :options="bricks" label="name" :value="0"
                        :reduce="(brick) => brick.id" placeholder="Select Bricks" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8">
                  <c-form-group>
                    <template #label> Visit Types </template>
                    <template #input>
                      <input label="All" id="VisitType" v-if="visit_types.length != 0" class="m-1" type="checkbox"
                        v-model="checkAllVisitTypes" title="Check All Visit Type" @change="checkAllVisitType" />
                      <label for="VisitType" v-if="visit_types.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="visit_type_id" :options="visit_types" label="name" :value="0"
                        :reduce="(visit_type) => visit_type.id" placeholder="Select Visit Type" class="mt-2" multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-8" v-if="another_types.length > 0">
                  <c-form-group>
                    <template #label> Another Types </template>
                    <template #input>
                      <input label="All" id="anotherType" v-if="another_types.length != 0" class="m-1" type="checkbox"
                        v-model="checkAllAnotherTypes" title="Check All Another Type" @change="checkAllAnotherType" />
                      <label for="anotherType" v-if="another_types.length != 0" style="font-weight: bold">All</label>
                      <v-select v-model="another_type_id" :options="another_types" label="name" :value="0"
                        :reduce="(another_type) => another_type.id" placeholder="Select Another Type" class="mt-2"
                        multiple />
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-8">
                  <c-form-group>
                    <template #label> Shift </template>
                    <template #input>
                      <v-select v-model="shift_id" :options="shifts" label="name" placeholder="Select Shift"
                        :reduce="(option) => option.id" class="mt-2" multiple/>
                    </template>
                  </c-form-group>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
        <c-tab>
          <template slot="title">
            <c-icon class="custom_icon" name="cil-clock" /> Duration Filters
          </template>
          <c-card>
            <c-card-body>
              <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-8" v-if="value == 2">
                  <c-form-group>
                    <template #label>
                      <strong>Under Time Filter</strong>
                      <small class="text-muted d-block">Show visits with duration less than line's minimum time</small>
                    </template>
                    <template #input>
                      <div class="custom-control custom-checkbox mt-2">
                        <input
                          type="checkbox"
                          class="custom-control-input"
                          id="underTimeFilter"
                          v-model="underTimeFilter"
                        />
                        <label class="custom-control-label" for="underTimeFilter">
                          <span class="font-weight-bold text-warning">
                            <c-icon name="cil-clock" class="mr-1"></c-icon>
                            Filter Under Time Visits
                          </span>
                        </label>
                      </div>
                      <small class="text-info mt-1 d-block" v-if="underTimeFilter">
                        <c-icon name="cil-info" class="mr-1"></c-icon>
                        Only visits with duration below the line's minimum time setting will be shown
                      </small>
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8" v-if="value == 2 && underTimeFilter">
                  <c-form-group>
                    <template #label>
                      <strong>Under Time Settings</strong>
                      <small class="text-muted d-block">Current minimum time per line</small>
                    </template>
                    <template #input>
                      <div class="under-time-info mt-2" v-if="underTimeSettings.length > 0">
                        <div
                          v-for="setting in filteredUnderTimeSettings"
                          :key="setting.id"
                          class="alert alert-info py-2 mb-2"
                        >
                          <strong>{{ setting.line_name }}:</strong>
                          <span class="badge badge-primary ml-2">{{ setting.time }} minutes</span>
                        </div>
                      </div>
                      <div v-else class="alert alert-warning py-2">
                        <c-icon name="cil-warning" class="mr-1"></c-icon>
                        No under time settings configured for selected lines
                      </div>
                    </template>
                  </c-form-group>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-8" v-if="value == 2">
                  <c-form-group>
                    <template #label>
                      <strong>Duration Range Filter</strong>
                      <small class="text-muted d-block">Filter by specific duration range (minutes)</small>
                    </template>
                    <template #input>
                      <div class="row">
                        <div class="col-6">
                          <c-input
                            type="number"
                            label="Min Duration"
                            v-model="minDuration"
                            placeholder="0"
                            min="0"
                          />
                        </div>
                        <div class="col-6">
                          <c-input
                            type="number"
                            label="Max Duration"
                            v-model="maxDuration"
                            placeholder="999"
                            min="0"
                          />
                        </div>
                      </div>
                    </template>
                  </c-form-group>
                </div>
              </div>
            </c-card-body>
          </c-card>
        </c-tab>
      </c-tabs>
    </c-card-body>
    <c-card-footer>
      <c-button color="primary" class="text-white" @click="show" style="float: right">Show</c-button>
    </c-card-footer>
  </c-card>
</template>
<script>
import moment from "moment";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
export default {
  components: {
    vSelect,
  },
  emits: ["getVisits"],
  data() {
    return {
      type_id: [],
      view: null,
      speciality_id: [],
      division_id: [],
      user_id: [],
      line_id: [],
      types: [],
      status: [
        { id: 1, name: "Total" },
        { id: 2, name: "Approved" },
        { id: 3, name: "Pending" },
      ],
      actualData: [
        { id: 1, name: "Total" },
        { id: 2, name: "UnPlanned" },
        { id: 3, name: "Planned" },
      ],
      actualProductData: [
        { id: 1, name: "Total" },
        { id: 2, name: "Follow up" },
        { id: 3, name: "market feedback" },
      ],
      actualDataId: 1,
      actualProductDataId: 1,
      status_id: 1,
      specialities: [],
      divisions: [],
      bricks: [],
      lines: [],
      visit_types: [],
      visit_type_id: [],
      another_types: [],
      another_type_id: [],
      users: [],
      shifts: [],
      shift_id: [],
      brick_id: null,
      planSetting: null,
      underTimeFilter: false,
      underTimeSettings: [],
      minDuration: null,
      maxDuration: null,
      allAccountTypes: true,
      checkAllSpecialities: true,
      checkAllDivisions: false,
      checkAllBricks: false,
      checkAllLines: false,
      checkAllUsers: false,
      checkAllVisitTypes: false,
      checkAllAnotherTypes: false,
      value: 2,
      from_date: moment().startOf("month").format("YYYY-MM-DD"),
      to_date: moment().endOf("month").format("YYYY-MM-DD"),
      options: [
        { id: 1, name: "Plan Visits" },
        { id: 2, name: "Actual Visits" },
        { id: 3, name: "Plan OW Visits" },
        { id: 4, name: "Actual OW Visits" },
      ],
    };
  },
  computed: {
    filteredUnderTimeSettings() {
      if (!this.line_id || this.line_id.length === 0) {
        return this.underTimeSettings;
      }
      return this.underTimeSettings.filter(setting =>
        this.line_id.includes(setting.line_id)
      );
    }
  },
  methods: {
    initialize() {
      axios
        .post("/api/get-lines-with-dates", {
          from: this.from_date,
          to: this.to_date,
          data: ['lines', 'shifts', 'accountTypes', 'users', 'visit_types', 'another_types', 'planSetting']
        })
        .then((response) => {
          this.lines = response.data.data.lines;
          this.users = response.data.data.users;
          this.planSetting = response.data.data.planSetting;
          this.visit_types = response.data.data.visit_types;
          this.another_types = response.data.data.another_types;
          this.shifts = response.data.data.shifts;
          this.types = response.data.data.accountTypes;
          this.type_id = this.types.map((item) => item.id);

          // Fetch under time settings
          this.fetchUnderTimeSettings();
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },

    fetchUnderTimeSettings() {
      axios
        .get("/api/under-time")
        .then((response) => {
          this.underTimeSettings = response.data.map(item => ({
            id: item.id,
            line_id: item.line_id || null,
            line_name: item.line || 'All Lines',
            time: item.under_time
          }));
        })
        .catch((error) => {
          console.warn('Could not fetch under time settings:', error);
          this.underTimeSettings = [];
        });
    },
    getAllData() {
      this.line_id = [];
      this.user_id = [];
      this.division_id = [];
      this.speciality_id = [];
      this.brick_id = [];
      this.initialize();
    },
    getLineData() {
      axios
        .post(`/api/get-lines-data-with-dates`, {
          lines: this.line_id,
          from: this.from_date,
          to: this.to_date,
          data: ['divisions', 'bricks', 'users', 'specialities']
        })
        .then((response) => {
          this.divisions = response.data.data.divisions;
          this.bricks = response.data.data.bricks;
          this.users = response.data.data.users;
          this.specialities = response.data.data.specialities;
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    checkAllLine() {
      if (this.checkAllLines) this.line_id = this.lines.map((item) => item.id);
      if (this.checkAllLines == false) this.line_id = null;
      this.getLineData();
    },
    checkAllAccounts() {
      if (this.allAccountTypes)
        this.type_id = this.types.map((item) => item.id);
      if (this.allAccountTypes == false) this.type_id = null;
    },
    checkAllDivs() {
      if (this.checkAllDivisions)
        this.division_id = this.divisions.map((item) => item.id);
      if (this.checkAllDivisions == false) this.division_id = null;
    },
    checkAllBrick() {
      if (this.checkAllBricks)
        this.brick_id = this.bricks.map((item) => item.id);
      if (this.checkAllBricks == false) this.brick_id = null;
    },
    checkAllVisitType() {
      if (this.checkAllVisitTypes)
        this.visit_type_id = this.visit_types.map((item) => item.id);
      if (this.checkAllVisitTypes == false) this.visit_type_id = null;
    },
    checkAllAnotherType() {
      if (this.checkAllAnotherTypes)
        this.another_type_id = this.another_types.map((item) => item.id);
      if (this.checkAllAnotherTypes == false) this.another_type_id = null;
    },
    checkAllEmployees() {
      if (this.checkAllUsers) this.user_id = this.users.map((item) => item.id);
      if (this.checkAllUsers == false) this.user_id = null;
    },
    checkAllSpech() {
      if (this.checkAllSpecialities)
        this.speciality_id = this.specialities.map((item) => item.id);
      if (this.checkAllSpecialities == false) this.speciality_id = null;
    },
    show() {
      let visitFilter = {
        lines: this.line_id,
        shifts: this.shift_id,
        status: this.status_id,
        divisions: this.division_id,
        bricks: this.brick_id,
        users: this.user_id,
        types: this.type_id,
        visit_types: this.visit_type_id,
        another_types: this.another_type_id,
        specialities: this.speciality_id,
        visitType: this.value,
        actualData: this.actualDataId,
        actualProductData: this.actualProductDataId,
        fromDate: this.from_date,
        toDate: this.to_date,
        // New duration filters
        underTimeFilter: this.underTimeFilter,
        minDuration: this.minDuration,
        maxDuration: this.maxDuration,
      };
      this.$emit("getVisits", { visitFilter });
    },
  },
  created() {
    this.initialize();
  },
};
</script>