<?php

namespace App\Http\Controllers;

use App\Account;
use App\AccountLines;
use App\DivisionType;
use App\Exceptions\CrmException;
use App\Http\Controllers\Controller;
use App\Line;
use App\Models\NewAccountDoctor;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class KolListPerAccountController extends ApiController
{
    public function index(Request $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $listFilter = $request->all();
        $status = $request->status;
        $line = Line::find($request->line_id);
        $division = $user->getKol($line) ?? null;
        $activeAccounts = collect();
        $fields = [];
        $accountIds = $status == 1 ?  AccountLines::where('account_lines.line_id', $line->id)
            ->where('account_lines.line_division_id', $division?->id)
            ->where('account_lines.from_date', '<=', (string)Carbon::now())
            ->where(fn($q) => $q->where('account_lines.to_date', '=', null)
                ->orWhere('account_lines.to_date', '>=', (string)Carbon::now()))->pluck('account_id')->unique()->values()->toArray() : [];
        if ($status == 2) {
            $fields = [
                "s",
                "account",
                "acc_class",
                "line",
                "division",
                "brick",
                "speciality",
            ];
            $activeAccounts = Account::select([
                'accounts.id as id',
                'accounts.id as account_id',
                'accounts.file_id as file_id',
                'lines.name as line',
                'lines.id as line_id',
                'line_divisions.name as division',
                'line_divisions.id as div_id',
                'line_divisions.is_kol',
                'users.id as user_id',
                DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
                DB::raw('IFNULL(group_concat(distinct crm_bricks.id),"") as brick_id'),
                DB::raw('IFNULL(crm_accounts.code,"") as acc_code'),
                DB::raw('IFNULL(crm_accounts.name,"") as account'),
                DB::raw('IFNULL(crm_a.name,"") as acc_class'),
                DB::raw('IFNULL(crm_a.id,"") as acc_class_id'),
                'account_types.name as account_type',
                DB::raw('IFNULL(group_concat(distinct crm_specialities.name),"") as speciality'),
                'account_lines.id as acc_line_id',
                'division_types.color'
            ])
                ->join('account_types', function ($join) use ($listFilter) {
                    $join->on('accounts.type_id', 'account_types.id')
                        ->whereIntegerInRaw('accounts.type_id', $listFilter['type_id']);
                })
                ->join(
                    'account_lines',
                    function ($join) use ($listFilter) {
                        $join->on('accounts.id', 'account_lines.account_id')
                            ->where('account_lines.line_id', $listFilter['line_id'])
                            ->whereNull('account_lines.deleted_at')
                            ->whereIn('account_lines.line_division_id', $listFilter['div_id'])
                            ->where('account_lines.from_date', '<=', (string)Carbon::now())
                            ->where(fn($q) => $q->where('account_lines.to_date', '=', null)
                                ->orWhere('account_lines.to_date', '>=', (string)Carbon::now()));
                        if (!empty($listFilter['brick_id'])) {
                            $join->whereIntegerInRaw('account_lines.brick_id', $listFilter['brick_id']);
                        }
                        if (!empty($listFilter['class_id'])) {
                            $join->where(fn($q) => $q->whereIntegerInRaw('account_lines.class_id', $listFilter['class_id'])
                                ->orWhereNull('account_lines.class_id'));
                        }
                    }
                )
                ->join(
                    'new_account_doctors',
                    function ($join) use ($listFilter) {
                        $join->on('accounts.id', '=', 'new_account_doctors.account_id');
                        $join->on('account_lines.id', '=', 'new_account_doctors.account_lines_id')
                            ->where('new_account_doctors.line_id', $listFilter['line_id'])
                            ->whereNull('new_account_doctors.deleted_at')
                            ->where('new_account_doctors.from_date', '<=', (string)Carbon::now())
                            ->where(fn($q) => $q->where('new_account_doctors.to_date', '=', null)
                                ->orWhere('new_account_doctors.to_date', '>=', (string)Carbon::now()));;
                    }
                )
                ->join('doctors', function ($join) use ($listFilter) {
                    $join->on('new_account_doctors.doctor_id', 'doctors.id')
                        ->whereNull('doctors.deleted_at')
                        ->whereIntegerInRaw('doctors.speciality_id', $listFilter['speciality_id']);
                })
                ->leftJoin('lines', 'account_lines.line_id', 'lines.id')
                ->leftJoin('line_divisions', 'account_lines.line_division_id', 'line_divisions.id')
                ->leftJoin('line_users_divisions', function ($join) {
                    $join->on('line_divisions.id', 'line_users_divisions.line_division_id')
                        ->where('line_users_divisions.from_date', '<=', Carbon::now())
                        ->where(fn($q) => $q->where('line_users_divisions.to_date', '=', null)
                            ->orWhere('line_users_divisions.to_date', '>=', (string)Carbon::now()));
                })
                ->leftJoin('users', 'line_users_divisions.user_id', 'users.id')
                ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
                ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
                ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
                ->leftJoin('classes as a', 'account_lines.class_id', 'a.id')
                ->where('accounts.deleted_at', null)
                ->where('accounts.hidden_fav_list', 0)
                ->orderBy('accounts.code', 'asc');
            $activeAccounts = $activeAccounts->groupBy(
                "accounts.id",
                "lines.id",
                "line_divisions.id",
                "users.id",
                "account_lines.id",
                "a.name",
                "a.id",
            )->get();
        } else {
            $activeAccounts = Account::select([
                'accounts.id as id',
                'accounts.id as account_id',
                'accounts.file_id as file_id',
                'lines.name as line',
                'lines.id as line_id',
                'line_divisions.name as division',
                'line_divisions.id as div_id',
                'line_divisions.is_kol',
                'users.id as user_id',
                DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
                DB::raw('IFNULL(group_concat(distinct crm_bricks.id),"") as brick_id'),
                DB::raw('IFNULL(crm_accounts.code,"") as acc_code'),
                DB::raw('IFNULL(crm_accounts.name,"") as account'),
                DB::raw('IFNULL(crm_a.name,"") as acc_class'),
                DB::raw('IFNULL(crm_a.id,"") as acc_class_id'),
                'account_types.name as account_type',
                'account_lines.id as acc_line_id',
                DB::raw('IFNULL(group_concat(distinct crm_specialities.name),"") as speciality'),
                DB::raw('DATE_FORMAT(crm_account_lines.from_date,"%d-%m-%Y") as from_date'),
                DB::raw('IFNULL(DATE_FORMAT(crm_account_lines.to_date,"%d-%m-%Y"),"") as to_date'),
                'account_lines.ll as ll',
                'account_lines.lg as lg',
                'division_types.color'
            ])
                ->join('account_types', function ($join) use ($listFilter) {
                    $join->on('accounts.type_id', 'account_types.id')
                        ->whereIntegerInRaw('accounts.type_id', $listFilter['type_id']);
                })
                ->join(
                    'account_lines',
                    function ($join) use ($listFilter) {
                        $join->on('accounts.id', 'account_lines.account_id')
                            ->where('account_lines.line_id', $listFilter['line_id'])
                            ->whereIn('account_lines.line_division_id', $listFilter['div_id'])
                            ->whereNull('account_lines.deleted_at');
                        if (!empty($listFilter['brick_id'])) {
                            $join->whereIntegerInRaw('account_lines.brick_id', $listFilter['brick_id']);
                        }
                        if (!empty($listFilter['class_id'])) {
                            $join->where(fn($q) => $q->whereIntegerInRaw('account_lines.class_id', $listFilter['class_id'])
                                ->orWhereNull('account_lines.class_id'));
                        }
                    }
                )
                ->join(
                    'new_account_doctors',
                    function ($join) use ($listFilter) {
                        $join->on('accounts.id', '=', 'new_account_doctors.account_id');
                        $join->on('account_lines.id', '=', 'new_account_doctors.account_lines_id')
                            ->where('new_account_doctors.line_id', $listFilter['line_id'])
                            ->whereNull('new_account_doctors.deleted_at');
                    }
                )
                ->join('doctors', function ($join) use ($listFilter) {
                    $join->on('new_account_doctors.doctor_id', 'doctors.id')
                        ->whereNull('doctors.deleted_at')
                        ->whereIntegerInRaw('doctors.speciality_id', $listFilter['speciality_id']);
                })
                ->leftJoin('lines', 'account_lines.line_id', 'lines.id')
                ->leftJoin('line_divisions', 'account_lines.line_division_id', 'line_divisions.id')
                ->leftJoin('line_users_divisions', function ($join) {
                    $join->on('line_divisions.id', 'line_users_divisions.line_division_id')
                        ->whereNull('line_users_divisions.deleted_at')
                        ->where('line_users_divisions.from_date', '<=', Carbon::now())
                        ->where(fn($q) => $q->where('line_users_divisions.to_date', '=', null)
                            ->orWhere('line_users_divisions.to_date', '>=', (string)Carbon::now()));
                })
                ->leftJoin('users', 'line_users_divisions.user_id', 'users.id')
                ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
                ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
                ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
                ->leftJoin('classes as d', 'doctors.class_id', 'd.id')
                ->leftJoin('classes as a', 'account_lines.class_id', 'a.id')
                ->where('accounts.deleted_at', null)
                ->where('doctors.deleted_at', null)
                ->where('accounts.hidden_fav_list', 0)
                ->whereNotIn('accounts.id', $accountIds)
                ->orderBy('accounts.code', 'asc')
                ->groupBy(
                    "accounts.id",
                    "lines.id",
                    "line_divisions.id",
                    "users.id",
                    "account_lines.id",
                    "a.name",
                    "a.id",
                )->get();
            $fields = [
                "s",
                "account",
                "acc_class",
                "line",
                "division",
                "brick",
                "speciality",
                "from_date",
                "to_date",
            ];
        }
        $dates[] = [
            'Month' => Carbon::now()->format('F'),
            'Year' => Carbon::now()->format('Y'),
        ];
        return $this->respond(['accounts' => $activeAccounts, 'dates' => $dates, 'fields' => $fields]);
    }

    public function store(Request $request)
    {
        $line = Line::find($request->line_id);
        $date = $request->status == 1
            ? Carbon::now()->firstOfMonth()->toDateString()
            : Carbon::now()->subMonth()->endOfMonth()->toDateString();
        /**@var User $user */
        $user = Auth::user();
        $division = $user->getKol($line);
        if (!$division) throw new Exception('This Manager does not have kol for this line');
        DB::transaction(function () use ($user, $date, $request, $division) {
            foreach ($request->accounts as $account) {
                $accountLine = AccountLines::where('account_id', $account['account_id'])
                    ->where('line_id', $account['line_id'])
                    ->where('class_id', (int)$account['acc_class_id'])
                    ->where('line_division_id', $division->id)
                    ->where('from_date', '<=', (string)Carbon::now())
                    ->where(fn($q) => $q->where('to_date', '=', null)->orWhere('to_date', '>=', (string)Carbon::now()))
                    ->latest()
                    ->first();
                if ($request->status == 1) {
                    if ($account['is_kol'] == 0 && !$accountLine) {
                        // throw new CrmException($account);

                        $newAccountLine = AccountLines::create([
                            'account_id' => $account['account_id'],
                            'line_id' => $account['line_id'],
                            'line_division_id' => $division->id,
                            'brick_id' => null,
                            'class_id' => (int)$account['acc_class_id'],
                            'from_date' => $date,
                            'to_date' => null,
                            'file_id' => $account['file_id'] ?? null,
                        ]);
                        $newAccountDoctors = NewAccountDoctor::where('account_id', $account['account_id'])
                            ->where('account_lines_id', $account['acc_line_id'])->where('line_id', $account['line_id'])->get();
                        foreach ($newAccountDoctors as $newAccountDoctor) {
                            NewAccountDoctor::create([
                                'account_id' => $account['account_id'],
                                'line_id' => $account['line_id'],
                                'doctor_id' => $newAccountDoctor->doctor_id,
                                'account_lines_id' => $newAccountLine->id,
                                'from_date' => $date,
                                'to_date' => null,
                            ]);
                        }
                    }
                } else {
                    if ($account['is_kol'] == 1 && $account['user_id'] == $user->id) {
                        AccountLines::find($account['acc_line_id'])->update(['to_date' => $date]);
                        $doctors = NewAccountDoctor::where('new_account_doctors.account_id', $account['account_id'])
                            ->where('new_account_doctors.line_id', $account['line_id'])
                            ->where('new_account_doctors.account_lines_id', $account['acc_line_id'])
                            ->whereNull('new_account_doctors.to_date')
                            ->latest()->get();
                        foreach ($doctors as $doctor) {
                            if ($doctor && Carbon::parse($doctor->from_date)->toDateString() <= $date) {
                                $doctor->update([
                                    'to_date' => $date,
                                ]);
                            } else {
                                throw new Exception('Doctor ' . $account['account'] . ' Active From: ' .
                                    Carbon::parse($doctor->from_date)->toDateString() . ' so it cannot be inactive at ' . $date);
                            }
                        }
                    }
                }
            }
        });
        return $this->respondSuccess();
    }

    public function resetKolList(Request $request)
    {
        $line = Line::find($request->line_id);
        /**@var User $user */
        $user = Auth::user();
        $division = $user->getKol($line);
        DB::transaction(function () use ($line, $division) {
            $accountLines = AccountLines::where('line_id', $line->id)->where('line_division_id', $division->id)->get()->pluck('id');
            NewAccountDoctor::where('line_id', $line->id)->whereIn('account_lines_id', $accountLines)->forceDelete();
            AccountLines::where('line_id', $line->id)->where('line_division_id', $division->id)->forceDelete();
        });

        return $this->respondSuccess();
    }
}
