<?php

namespace App\Http\Controllers;

use App\ApprovablePlanable;
use App\DivisionType;
use App\Exceptions\CrmException;
use App\Helpers\Notifications\NotificationHelper;
use App\Http\Requests\ApprovalEmployeesRequest;
use App\Http\Requests\VacationDisapproveRequest;
use App\Line;
use App\Models\ApprovalFlowUser;
use App\Models\ApprovalSetting;
use App\Models\StartExpenseMonth;
use App\Notifications\VacationApprovedNotification;
use App\PlanSetting;
use App\PlanVisit;
use App\PlanVisitDetails;
use App\Position;
use App\Reason;
use App\Reasonable;
use App\Services\PlanService;
use App\User;
use App\Vacation;
use App\VacationSetting;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class VacationApprovalController extends ApiController
{
    public function getVacations(Request $request)
    {
        $from_date = null;
        $to_date = null;
        $reasons = Reason::where('request_type', Vacation::class)->get();
        $vacations = collect([]);
        if ($request->flag == 1) {
            $user = User::find($request->user);
            $vacations =  Vacation::where('user_id', $user->id)
                // ->where('from_date', '>=', Carbon::now()->toDateString())
                ->whereHas('details', function ($q) {
                    $q->whereNull('approval');
                })
                ->get();
        } else {
            $from_date = Carbon::parse($request->from_date)->startOfDay();
            $to_date = Carbon::parse($request->to_date)->endOfDay();
            $vacations = Vacation::whereIn('user_id', !empty($request->users_id) ? $request->users_id : $request->codes_id)
                ->where(fn($q) => $q->whereBetween('from_date', [$from_date, $to_date])
                    ->orwhereBetween('to_date', [$from_date, $to_date]))
                ->whereHas('details', function ($q) {
                    $q->whereNull('approval');
                })
                ->get();
            if (count($request->vacation_type_id) > 0) {
                $vacations = $vacations->whereIn('vacation_type_id', $request->vacation_type_id);
            }
        }
        $approvalSetting = ApprovalSetting::where('key', 'vacation_approval_center_flow')->value('value');
        $vactionSetting = VacationSetting::where('key', 'vacation_approval')->value('value');
        $vacationTime = StartExpenseMonth::where('name', $vactionSetting)->value('day');
        /**@var User $authUser */
        $authUser = Auth::user();
        $now = Carbon::now()->format('Y-m');
        $approvalData = $authUser->userApprovals($from_date, $to_date);
        $lines = $approvalData['lines'];
        $linesAapprovables = $approvalData['linesAapprovables'];
        $required = 0;
        $dataFlow = '';
        $scanLevel = 1;
        $vacations = $vacations->filter(
            function (Vacation $vacation) use ($vacationTime, $now, $from_date, $to_date, $lines, $linesAapprovables, $approvalSetting, $scanLevel, $authUser, &$required, $dataFlow) {
                if ($approvalSetting == 'No') {
                    $vacationToDate = Carbon::parse($vacation->to_date)->addMonth($vacationTime)->format('Y-m');
                    if ($vacationToDate < $now) return;
                    return is_null($vacation->details?->approval);
                } else {
                    if (isNullable($vacation->details?->approval)) {
                        $approvablesCountOnThisShit = $vacation->details?->approvalFlows()->count();
                        $data = $authUser->approvalWidget($vacation, $authUser, Vacation::class, $from_date, $to_date, $lines, $linesAapprovables);
                        $dataFlow = $data['linesDataFlow'];
                        $currentFlow = $dataFlow?->flow;
                        $required = $dataFlow?->required;
                        $showData = $dataFlow?->show_data;
                        $vacantCount = $data['vacantCount'];
                        $haveToApprove = $approvablesCountOnThisShit === $currentFlow - ($vacantCount + $scanLevel);
                        return $haveToApprove || $required || $showData;
                    }
                }
            }
        )
            ->map(function ($vacation) use ($reasons, $required) {
                $vacationUser = $vacation->user;
                return [
                    'vacation' => $vacation->id,
                    'id' => $vacation->id,
                    'user' => $vacationUser->fullname,
                    'code' => $vacationUser->emp_code ?? '',
                    'type' => $vacation->type->name,
                    'from' => Carbon::parse($vacation->from_date)->format('Y-m-d'),
                    'to' => Carbon::parse($vacation->to_date)->format('Y-m-d'),
                    'shift' => $vacation->full_day == 0 ? $vacation->shift?->name : 'Full-Day',
                    'approval' => $vacation->details?->approval,
                    'required' => $required,
                    'visitable_type' => Vacation::class,
                    'reasons' => $reasons,
                    'reason_id' => null
                ];
            });
        return $this->respond($vacations->values());
    }

    public function accept(Request $request)
    {
        $approvalSetting = ApprovalSetting::where('key', 'vacation_approval_center_flow')->value('value');
        $all_vacations = $request->vacations;
        /**@var User $authUser */
        $authUser = Auth::user();
        $users = collect([]);
        $vacations = collect([]);
        foreach ($all_vacations as $vacation) {
            $objVacation = resolve($vacation['visitable_type'])->find($vacation['visitable_id']);
            $vacations = $vacations->push($objVacation);
            $users->push($objVacation->user);
            $detail = $objVacation->details;
            if ($approvalSetting == 'Yes') {
                ApprovalFlowUser::firstOrCreate([
                    'detail_id' => $detail->id,
                    'user_id' => $authUser->id,
                    'approval' => 1,
                ]);
                if ($vacation['required'] == 1) {
                    $detail->user_id = $authUser->id;
                    $detail->approval = 1;
                    $detail->save();
                    $this->deletePlansAfterApproveVacation($objVacation);
                }
            } else {
                $detail->user_id = $authUser->id;
                $detail->approval = 1;
                $detail->save();
                $this->deletePlansAfterApproveVacation($objVacation);
            }
        }
        NotificationHelper::send(
            $users->unique('id'),
            new VacationApprovedNotification('Vacation Get Approved', auth()->user())
        );
        // LogActivity::addLog();
        return response()->json(['status' => 'success']);
    }
    public function deletePlansAfterApproveVacation($vacation)
    {
        $planSetting = PlanSetting::where('key', 'delete_plans_if_user_create_vacation')->value('value');
        $perShift = PlanSetting::where('key', 'plan_shift')->value('value') == 'yes';
        if ($planSetting == 'yes') {
            $plans = collect([]);
            $period = CarbonPeriod::create($vacation->from_date, $vacation->to_date);
            foreach ($period as $date) {
                $user = User::find($vacation->user_id);
                $plans = (new PlanService)->getUserPlansPerDate($user, $date);
                if ($perShift && !isNullable($vacation->shift_id)) {
                    $planIds = $plans->where('shift_id', $vacation->shift_id)->pluck('id');
                    PlanVisitDetails::whereIn('visitable_id', $planIds)->where('visitable_type', PlanVisit::class)->delete();
                    Reasonable::whereIn('reasonable_id', $planIds)->where('reasonable_type', PlanVisit::class)->delete();
                    $plans->where('shift_id', $vacation->shift_id)->each(fn($plan) => $plan->forceDelete());
                }
                if (!$perShift && !isNullable($vacation->shift_id)) {
                    $planIds = $plans->where('acc_type_shift_id', $vacation->shift_id)->pluck('id');
                    PlanVisitDetails::whereIn('visitable_id', $planIds)->where('visitable_type', PlanVisit::class)->delete();
                    Reasonable::whereIn('reasonable_id', $planIds)->where('reasonable_type', PlanVisit::class)->delete();
                    $plans->where('acc_type_shift_id', $vacation->shift_id)->each(fn($plan) => $plan->forceDelete());
                }
                if (isNullable($vacation->shift_id) && $vacation->full_day == 1) {
                    $planIds = $plans->pluck('id');
                    PlanVisitDetails::whereIn('visitable_id', $planIds)->where('visitable_type', PlanVisit::class)->delete();
                    Reasonable::whereIn('reasonable_id', $planIds)->where('reasonable_type', PlanVisit::class)->delete();
                    $plans->each(fn($plan) => $plan->forceDelete());
                }
            }
        }
    }
    public function reject(VacationDisapproveRequest $request)
    {
        $approvalSetting = ApprovalSetting::where('key', 'vacation_approval_center_flow')->value('value');
        $vacationSetting = VacationSetting::where('key', 'vacation_disapprove_automatic')->value('value');
        $users = collect([]);
        /**@var User $authUser */
        $authUser = Auth::user();
        foreach ($request->vacations as $vacation) {
            $objVacation = resolve($vacation['visitable_type'])->find($vacation['visitable_id']);
            $users->push($objVacation->user);
            $detail = $objVacation->details;
            if ($approvalSetting == 'Yes') {
                ApprovalFlowUser::firstOrCreate([
                    'detail_id' => $detail->id,
                    'user_id' => $authUser->id,
                    'approval' => 0,
                ]);
                if ($vacation['required'] == 1) {
                    $detail->user_id = $authUser->id;
                    $detail->approval = 0;
                    $detail->save();
                }
                if ($vacationSetting == 'Yes') {
                    $detail->user_id = $authUser->id;
                    $detail->approval = 0;
                    $detail->save();
                }
            } else {
                $detail->user_id = $authUser->id;
                $detail->approval = 0;
                $detail->save();
            }
            if ($vacation['reason_id'] != null) {
                $reason = Reason::find($vacation['reason_id']);
                $objVacation->reasons()->attach($reason, [
                    'user_id' => $authUser->id,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }
        }

        return response()->json(['status' => 'reject']);
    }
    public function filterOfEmployees(Request $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $users = collect([]);
        $types = [DivisionType::class, Position::class];
        foreach ($request->line_id as $line) {
            foreach ($types as $type) {
                $users = $users->merge($user->planableUsers($line, Vacation::class, $type));
            }
        }
        return response()->json([
            'filtered_users' => $users->unique('id')->values(),
            'codes' => $users->unique('id')->filter(fn($user) => $user->emp_code != null)->values()
        ]);
    }
}
