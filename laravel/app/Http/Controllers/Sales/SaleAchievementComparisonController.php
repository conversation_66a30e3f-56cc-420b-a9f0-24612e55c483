<?php

namespace App\Http\Controllers\Sales;

use App\DivisionType;
use App\Http\Controllers\ApiController;
use App\Line;
use App\Services\Reports\SalesComparison\SalesAchievementComparisonService;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;

class SaleAchievementComparisonController extends ApiController
{


    public function __construct(private readonly SalesAchievementComparisonService $salesAchievementComparisonService)
    {
    }

    public function getValidLines(Request $request)
    {
        $from = Carbon::parse($request->from)->startOfDay();
        $to = Carbon::parse($request->to)->endOfDay();

        return $this->respond($this->salesAchievementComparisonService->getLines($from,$to));
    }

    public function getFilterDivisionTypes(Request $request)
    {
        $from = Carbon::parse($request->from)->startOfDay();
        $to = Carbon::parse($request->to)->endOfDay();
        $line = Line::findOrFail($request->line_id);

        return $this->respond($this->salesAchievementComparisonService->getFiltersData(
            $from,
            $to,
            $line,
        ));
    }

    public function getReportData(Request $request)
    {
        $from = Carbon::parse($request->from)->startOfDay();
        $to = Carbon::parse($request->to)->endOfDay();
        $line = Line::findOrFail($request->line_id);
        $divisionType = DivisionType::findOrFail($request->division_type_id);

        return $this->respond($this->salesAchievementComparisonService->getReportData(
            $from,
            $to,
            $line,
            $divisionType,
        ));
    }
}
