<?php

namespace App;

use App\Exceptions\CrmException;
use App\Models\Distributors\DistributorLine;
use App\Models\ProductWeight;
use App\Traits\ModelExportable;
use App\Traits\ModelImportable;
use App\Traits\PdfExportable;
use App\Traits\RestoreData;
use App\Traits\SendMail;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use App\Models\DoctorProfiling;
use App\Models\Material;
use Illuminate\Database\Eloquent\Concerns\HasRelationships;
use Illuminate\Support\Facades\DB;
use Staudenmeir\EloquentHasManyDeep\HasManyDeep;
use Staudenmeir\EloquentHasManyDeep\HasRelationships as EloquentHasManyDeepHasRelationships;

class Line extends Model
{
    use SoftDeletes;
    use ModelImportable;
    use ModelExportable;
    use SendMail;
    use PdfExportable;
    use RestoreData;
    use HasRelationships;
    use EloquentHasManyDeepHasRelationships;

    protected $guard_name = 'api';

    protected $table = 'lines';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'currency_id',
        'timezone',
        'country_id',
        'name',
        'notes',
        'sort',
        'from_date',
        'to_date',
        'file_id'
    ];


    protected $casts = [
        'from_date' => 'datetime',
        'to_date' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }

    public function country()
    {
        return $this->belongsTo(Country::class, 'country_id');
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class, 'currency_id');
    }

    public function materials()
    {
        $this->hasMany(Material::class);
    }

    public function divisions($from = null, $to = null)
    {
        $from = $from ? Carbon::parse($from)->startOfDay() : Carbon::now()->startOfDay();
        $to = $to ? Carbon::parse($to)->endOfDay() : Carbon::now()->endOfDay();
        return $this->hasMany(LineDivision::class)->whereNull('deleted_at')
            // ->where('from_date', '<=', $from)
            //     ->where(fn($q) => $q->where('to_date', '>=', $to)
            //         ->orWhere('to_date', null));
            ->where(function ($query) use ($from, $to) {
                $query->where(function ($subQuery) use ($from, $to) {
                    $subQuery->whereNull('line_divisions.to_date') // Active records
                    ->orWhereBetween('line_divisions.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                    ->orWhere('line_divisions.to_date', '>=', $to->toDateString()); // Ends within range
                })
                    ->where(function ($subQuery) use ($from, $to) {
                        $subQuery->where('line_divisions.from_date', '<=', $from->toDateString()) // Starts before range
                        ->orWhereBetween('line_divisions.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                    });
            });
    }

    public function linedivisiontypes()
    {
        return $this->hasMany(LineDivisionType::class);
    }

    public function lineproducts()
    {
        return $this->hasMany(LineProduct::class);
    }

    public function lineusers()
    {
        return $this->hasMany(LineUser::class);
    }

    public function linedivusers()
    {
        return $this->hasMany(LineDivisionUser::class);
    }

    public function accountLines()
    {
        return $this->hasMany(AccountLines::class);
    }

    public function doctorProfiling()
    {
        return $this->hasMany(DoctorProfiling::class);
    }

    public function accounts()
    {
        return $this->belongsToMany(Account::class, 'account_lines')
            ->withPivot(["from_date", "to_date", "deleted_at"])
            ->where(fn($q) => $q->where('account_lines.to_date', '>', (string)Carbon::now())
                ->orWhere('account_lines.to_date', null));
    }

    public function accountTypes()
    {
        return $this->hasManyDeepFromRelations($this->accounts(), (new Account())->type())->distinct();
    }

    public function doctors()
    {
        return $this->belongsToMany(Doctor::class, 'new_account_doctors')
            ->withPivot(["from_date", "to_date", "deleted_at"])
            ->where(fn($q) => $q->where('new_account_doctors.to_date', '>', (string)Carbon::now())
                ->orWhere('new_account_doctors.to_date', null));
    }

    public function distributers()
    {
        return $this->belongsToMany('App\Distributer', 'distributer_lines')->withPivot(["from_date", "to_date", "deleted_at"])
            ->where(fn($q) => $q->where('distributer_lines.to_date', '>', (string)Carbon::now())
                ->orWhere('distributer_lines.to_date', null));
    }

    public static function lineDistributors($lines, $from = null, $to = null)
    {
        return DistributorLine::whereIntegerInRaw('line_id', $lines)
            ->where('from_date', '<=', $from?->toDateString() ?? (string)Carbon::now())
            ->where(fn($q) => $q->where('to_date', null)->orWhere('to_date', '>=', $to?->toDateString() ?? (string)Carbon::now()))
            ->with('distributor:id,name')->get()->pluck('distributor')->filter(fn($distributor) => $distributor != null)->unique('id')->values();
    }

    public function lineClasses()
    {
        return $this->hasMany(LineClasses::class);
    }

    public function products($from = null, $to = null)
    {
        $from = $from ? Carbon::parse($from)->startOfDay() : Carbon::now()->startOfDay();
        $to = $to ? Carbon::parse($to)->endOfDay() : Carbon::now()->endOfDay();
        return $this->belongsToMany(Product::class, 'line_products')->withPivot(["from_date", "to_date", "deleted_at"])
            ->whereNull('line_products.deleted_at')
            ->where(function ($query) use ($from, $to) {
                $query->where(function ($subQuery) use ($from, $to) {
                    $subQuery->whereNull('line_products.to_date') // Active records
                    ->orWhereBetween('line_products.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                    ->orWhere('line_products.to_date', '>=', $to->toDateString()); // Ends within range
                })
                    ->where(function ($subQuery) use ($from, $to) {
                        $subQuery->where('line_products.from_date', '<=', $from->toDateString()) // Starts before range
                        ->orWhereBetween('line_products.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                    });
            });
        // ->where('line_products.from_date', '<=', Carbon::parse($from)->toDateString() ?? (string)Carbon::now())
        // ->where(fn($q) => $q->where('line_products.to_date', '>=', Carbon::parse($to)->toDateString() ?? (string)Carbon::now())
        //     ->orWhere('line_products.to_date', null));
    }

    public function getProductName($product, $level)
    {
        if ($level == 'Product')
            return $product->name;
        elseif ($level == 'Brief')
            return $product->short_name;
        else
            return count($product->brands) > 0 ? $product->brands->first()?->name : $product->name;
    }

    public function productsOrBrands($from = null, $to = null)
    {
        $from = $from ? Carbon::parse($from)->startOfDay() : Carbon::now()->startOfDay();
        $to = $to ? Carbon::parse($to)->endOfDay() : Carbon::now()->endOfDay();
        $product_brand_level = Setting::where('key', 'reports_level')->value('value');
        return $this->products($from, $to)->get()->unique('id')->values()->map(function ($product) use ($product_brand_level) {
            return [
                'id' => $product->id,
                'name' => $this->getProductName($product, $product_brand_level),
            ];
        })->unique('name')->filter(fn($product) => $product['name'] != null)->values();
    }

    public function allProducts()
    {
        return $this->belongsToMany(Product::class, 'line_products')->withPivot(["from_date", "to_date", "deleted_at"])
            ->whereNull('line_products.deleted_at');
    }

    public function brands(): HasManyDeep
    {
        return $this->hasManyDeepFromRelations($this->products(), (new Product())->brands())
            ->select(DB::raw("distinct crm_brands.id,crm_brands.name"));
    }

    public function giveaways()
    {
        return $this->hasMany(Giveaway::class);
    }

    public function users($from = null, $to = null)
    {
        $from = $from ? Carbon::parse($from)->startOfDay() : Carbon::now()->startOfDay();
        $to = $to ? Carbon::parse($to)->endOfDay() : Carbon::now()->endOfDay();
        return $this->belongsToMany(User::class, 'line_users')->withPivot(["from_date", "to_date", "deleted_at"])
            ->whereNull('line_users.deleted_at')
            // ->where('line_users.from_date', '<=', $from)
            // ->where(fn($q) => $q->where('line_users.to_date', '>=', $to)
            //     ->orWhere('line_users.to_date', null));


            ->where(function ($query) use ($from, $to) {
                $query->where(function ($subQuery) use ($from, $to) {
                    $subQuery->whereNull('line_users.to_date') // Active records
                    ->orWhereBetween('line_users.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                    ->orWhere('line_users.to_date', '>=', $to->toDateString()); // Ends within range
                })
                    ->where(function ($subQuery) use ($from, $to) {
                        $subQuery->where('line_users.from_date', '<=', $from->toDateString()) // Starts before range
                        ->orWhereBetween('line_users.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                    });
            });
    }

    public function allUsers()
    {
        return $this->belongsToMany(User::class, 'line_users')->withPivot(["from_date", "to_date", "deleted_at"])
            ->whereNull('line_users.deleted_at');
    }

    public function divisionTypes(?Carbon $from = null,?Carbon $to = null): BelongsToMany
    {
        $from = $from ? Carbon::parse($from)->startOfDay() : Carbon::now()->startOfDay();
        $to = $to ? Carbon::parse($to)->endOfDay() : Carbon::now()->endOfDay();
        return $this->belongsToMany(DivisionType::class, 'line_division_types', 'line_id', 'divisiontype_id')
            ->withPivot(["from_date", "to_date", "deleted_at"])
            ->where('from_date', '<=', $from->toDateString())
            ->where(fn($q) => $q->where('line_division_types.to_date', '>=', $to->toDateString())
                ->orWhere('line_division_types.to_date', null));
    }

    public function classes()
    {
        return $this->belongsToMany(Classes::class, 'line_classes', 'line_id', 'class_id')
            ->withPivot(["from_date", "to_date", "deleted_at"])
            ->whereNull('line_classes.deleted_at')
            ->where('line_classes.from_date', '<=', now())
            ->where(fn($q) => $q->where('line_classes.to_date', '>', (string)Carbon::now())
                ->orWhere('line_classes.to_date', null));
    }

    public function specialities()
    {
        return $this->belongsToMany(Speciality::class, 'line_specialities')->withPivot(["from_date", "to_date", "deleted_at"])
            ->whereNull('line_specialities.deleted_at')
            ->where('line_specialities.from_date', '<=', now())
            ->where(fn($q) => $q->where('line_specialities.to_date', '>', (string)Carbon::now())
                ->orWhere('line_specialities.to_date', null));
    }

    public function userPositions()
    {
        return $this->belongsToMany(UserPosition::class, 'line_user_positions', 'line_id', 'user_position_id');
    }

    public function positions()
    {
        // return $this->hasManyDeepFromRelations($this->userPositions(),(new Position()));
        return $this->hasManyThrough(Position::class, UserPosition::class, 'position_id', 'id');
    }

    public function bricks()
    {
        return $this->belongsToMany(Brick::class, 'line_bricks')->withTimestamps()
            ->withPivot(['line_division_id', 'from_date', 'to_date', 'deleted_at'])
            ->where(fn($q) => $q->where('line_bricks.to_date', '>', (string)Carbon::now())
                ->orWhere('line_bricks.to_date', null));
    }

    public function lineBricks()
    {
        return $this->hasMany(LineBricks::class)
            ->where(fn($q) => $q->where('to_date', '>', (string)Carbon::now())
                ->orWhere('to_date', null));
    }

    public function lineBricksInDates(Carbon|null $from_date = null, Carbon|null $to_date = null): HasMany
    {
        $fromDate = $from_date ?? now();
        $toDate = $to_date ?? now();

        return $this->hasMany(LineBricks::class)
            ->where('from_date', '<=', $fromDate)
            ->where(fn($q) => $q->where('to_date', '>', $toDate)
                ->orWhere('to_date', null));
    }

    public function distributors()
    {
        return $this->belongsToMany(Distributor::class, 'line_distributors')->withTimestamps()
            ->withPivot(['from_date', 'to_date'])
            ->where('from_date', '<=', now())
            ->where(fn($q) => $q->where('line_distributors.to_date', '>', (string)Carbon::now())
                ->orWhere('line_distributors.to_date', null));
    }

    public function startPlanDay()
    {
        return $this->belongsToMany(User::class, 'user_start_plan_days', 'line_id', 'user_id');
    }

    public function isAvailable($from, $to)
    {
        if ($this->from_date->toDateString() <= $from) {
            if (!$this->to_date)
                return true;
            elseif ($this->to_date->toDateString() && ($this->to_date->toDateString() > $to && $this->to_date->toDateString() > $from))
                return true;
            else
                return false;
        } else return false;
    }

    public static function userLines(User $user)
    {
        if ($user->hasRole('admin') || $user->hasRole('sub admin') || $user->hasRole('Gemstone Admin')) $lines = Line::get();
        else {
            $lines = $user->lines()->wherePivot('deleted_at', null)->get()->filter(function ($line) {
                return $line->pivot->to_date === null || $line->pivot?->to_date >= (string)Carbon::now();
            });
        }
        return $lines;
    }

    public static function userDivisions(User $user, Line $line)
    {
        if ($user->hasRole('admin') || $user->hasRole('sub admin') || $user->hasRole('Gemstone Admin')) {
            $division_type = DivisionType::where('last_level', '=', 1)->first();
            $divisions = $line->divisions()->where('division_type_id', '=', $division_type->id)->where('deleted_at', null)->get()->filter(function ($lineDivision) {
                return $lineDivision->to_date === null || $lineDivision->to_date >= (string)Carbon::now();
            });
        } else {
            $divisions = $user->divisions()->wherePivot('line_id', $line->id)->wherePivot('deleted_at', null)->get()->filter(function ($line) {
                return $line->pivot->to_date === null || $line->pivot?->to_date >= (string)Carbon::now();
            });
        }
        return $divisions;
    }

    public function inactiveUsers()
    {
        return $this->belongsToMany(User::class, 'line_users')->withPivot(["from_date", "to_date", "deleted_at"])
            ->whereNull('line_users.deleted_at')
            ->where(fn($q) => $q->where('line_users.to_date', '<', (string)Carbon::now())
                ->orWhere('line_users.from_date', '>', now()));
    }

    public function getLinePositions()
    {
        return LineUserPosition::where('line_id', $this->id)->with('userPosition.position')->get()->pluck('userPosition.position')
            ->unique('id')->values();
    }

    public function restore()
    {
        $this->withTrashed()->where('id', $this->id)->restore();
        LineDivisionType::withTrashed()->where('line_id', $this->id)->restore();
        LineDivision::withTrashed()->where('line_id', $this->id)->restore();
        $lineDivisions = LineDivision::where("line_id", $this->id)->pluck('id');
        foreach ($lineDivisions as $lineDivision) {
            LineDivParent::withTrashed()->where("parent_id", $lineDivision)->restore();
        }
        LineProduct::withTrashed()->where('line_id', $this->id)->restore();
        LineUser::withTrashed()->where('line_id', $this->id)->restore();
        LineDivisionUser::withTrashed()->where('line_id', $this->id)->restore();
        LineBricks::withTrashed()->where('line_id', $this->id)->restore();
        LineSpecialities::withTrashed()->where('line_id', $this->id)->restore();
        LineClasses::withTrashed()->where('line_id', $this->id)->restore();
    }

    public function forceDelete()
    {
        LineDivisionType::withTrashed()->where('line_id', $this->id)->forceDelete();
        LineDivision::withTrashed()->where('line_id', $this->id)->forceDelete();
        $lineDivisions = LineDivision::where("line_id", $this->id)->pluck('id');
        foreach ($lineDivisions as $lineDivision) {
            LineDivParent::withTrashed()->where("parent_id", $lineDivision)->forceDelete();
        }
        LineProduct::withTrashed()->where('line_id', $this->id)->forceDelete();
        LineUser::withTrashed()->where('line_id', $this->id)->forceDelete();
        LineDivisionUser::withTrashed()->where('line_id', $this->id)->forceDelete();
        LineBricks::withTrashed()->where('line_id', $this->id)->forceDelete();
        LineSpecialities::withTrashed()->where('line_id', $this->id)->forceDelete();
        LineClasses::withTrashed()->where('line_id', $this->id)->forceDelete();
        $this->withTrashed()->where('id', $this->id)->forceDelete();
    }


    public function productWeights(): HasMany
    {
        return $this->hasMany(ProductWeight::class);
    }
}
