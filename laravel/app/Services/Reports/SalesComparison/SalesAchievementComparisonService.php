<?php

namespace App\Services\Reports\SalesComparison;

use App\DivisionType;
use App\Line;
use App\LineDivision;
use App\SaleDetail;
use App\Services\Structure\Repositories\LineDivisionRepository;
use App\Services\Structure\Repositories\LineDivisionRepositoryInterface;
use App\TargetDetails;
use Carbon\CarbonPeriod;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class SalesAchievementComparisonService
{

    private readonly int $cacheTimeout;

    public function __construct(private readonly LineDivisionRepositoryInterface $lineDivisionRepository)
    {
        $this->cacheTimeout = 60 * 60 * 24;
    }

    public function getFiltersData(Carbon $from, Carbon $to, Line $line): Collection
    {
        return Cache::remember(
            'division_types_in_line:' . $line->id,
            $this->cacheTimeout,
            fn() => $line->divisionTypes($from, $to)->whereNotIn('level', [
                DivisionType::min('level'),
                DivisionType::max('level')
            ])->get()
        );
    }

    public function getLines(Carbon $from, Carbon $to): Collection
    {
        $user = Auth::user();

        return Cache::remember('user_lines_with_id:' . $user->id, $this->cacheTimeout, fn() => $user->userLines($from, $to));
    }

    public function getReportData(Carbon $from, Carbon $to, Line $line, DivisionType $divisionType): array
    {

        $period = CarbonPeriod::create($from, "1 month", $to);

        $reportData = collect();
        $monthlyTotals = [];
        $dates = [];


        $divisions = $line
            ->divisions($from, $to)
            ->where("division_type_id", $divisionType->id)
            ->get();


        foreach ($divisions as $division) {
            [$salesSum, $targetSum] = $this->getSalesTargetPerDate($division, $divisionType, $period, $dates);
            $divisionReport = $this->makeHolder($salesSum, $targetSum, $division, $divisionType);
            $divisionData = $divisionReport->getHolder();

            // Calculate monthly totals (vertical totals)
            $this->addToMonthlyTotals($divisionData, $monthlyTotals);

            $reportData->push($divisionData);
        }

        $reportData = $reportData->sortBy('total_achievement', descending: true)->values();

        $reportData = $this->addTop3Colors($reportData);

        $totalsRow = $this->calculateTotalsRow($monthlyTotals);

        $reportData->push($totalsRow);


        $fieldDefinitions = $this->generateFieldDefinitions($period);

        return [
            'data' => $reportData,
            'fields' => $fieldDefinitions
        ];

    }

    /**
     * @param mixed $division
     * @param DivisionType $divisionType
     * @param CarbonPeriod $period
     * @param array $dates
     * @return array
     */
    private function getSalesTargetPerDate(mixed $division, DivisionType $divisionType, CarbonPeriod $period, array $dates): array
    {
        $lastLevelDivisions = $this->lineDivisionRepository
            ->getDeepestDescendantsAtLevel($division, $divisionType);

        $ids = $lastLevelDivisions->pluck("id");

        $saleQuery = SaleDetail::query()->whereIn("div_id", $ids);
        $targetQuery = TargetDetails::query()->whereIn("div_id", $ids);

        foreach ($period as $date) {
            $dates[] = ["$date->year", "$date->month"];
        }

        $salesSum = $saleQuery
            ->whereTupleIn([DB::raw("YEAR(date)"), DB::raw("MONTH(date)")], $dates)
            ->select([
                DB::raw("SUM(quantity) as units"),
                DB::raw("CONCAT(YEAR(date), '-', LPAD(MONTH(date), 2, '0')) as date_key"),
                DB::raw("YEAR(date) as year"),
                DB::raw("MONTH(date) as month"),
            ])
            ->groupBy('date')
            ->get()
            ->keyBy('date_key');

        $targetSum = $targetQuery
            ->whereTupleIn([DB::raw("YEAR(date)"), DB::raw("MONTH(date)")], $dates)
            ->select([
                DB::raw("SUM(target) as units"),
                DB::raw("CONCAT(YEAR(date), '-', LPAD(MONTH(date), 2, '0')) as date_key"),
                DB::raw("YEAR(date) as year"),
                DB::raw("MONTH(date) as month"),
            ])
            ->groupBy('date')
            ->get()
            ->keyBy('date_key');

        return [
            $salesSum,
            $targetSum,
        ];
    }

    public function makeHolder(Collection $salesSum, Collection $targetSum, LineDivision $division, DivisionType $divisionType): object
    {
        return new class($division, $divisionType, $salesSum, $targetSum) {
            public function __construct(
                private readonly LineDivision $division,
                private readonly DivisionType $divisionType,
                private readonly Collection   $salesSum,
                private readonly Collection   $targetSum
            )
            {
            }

            public function getHolder(): array
            {
                $result = [
                    'division_id' => $this->division?->id ?? ' ',
                    'division_name' => $this->division->name ?? ' ',
                    'division_type_name' => $this->divisionType->name ?? ' ',
                ];

                // Get all unique date keys from both sales and targets
                $allDateKeys = $this->salesSum->keys()
                    ->merge($this->targetSum->keys())
                    ->unique()
                    ->sort();

                $totalSales = 0;
                $totalTarget = 0;

                foreach ($allDateKeys as $dateKey) {
                    $sales = $this->salesSum?->get($dateKey);
                    $target = $this->targetSum?->get($dateKey);

                    $salesUnits = $sales ? (float)$sales->units : 0;
                    $targetUnits = $target ? (float)$target->units : 0;

                    $achievement = $targetUnits > 0 ? ($salesUnits / $targetUnits) * 100 : 0;

                    // Create column names for each month
                    $monthKey = str_replace('-', '_', $dateKey);

                    $result["sales_$monthKey"] = round($salesUnits, 2);
                    $result["target_$monthKey"] = round($targetUnits, 2);
                    $result["achievement_$monthKey"] = round($achievement, 2);

                    // Add to horizontal totals
                    $totalSales += $salesUnits;
                    $totalTarget += $targetUnits;
                }


                // Add horizontal totals (total for this division across all months)
                $overallAchievement = $totalTarget > 0 ? ($totalSales / $totalTarget) * 100 : 0;
                $result['total_sales'] = round($totalSales, 2);
                $result['total_target'] = round($totalTarget, 2);
                $result['total_achievement'] = round($overallAchievement, 2);

                return $result;
            }
        };

    }


    /**
     * Add division data to monthly totals for vertical totals calculation
     */
    private function addToMonthlyTotals(array $divisionData, array &$monthlyTotals): void
    {
        foreach ($divisionData as $key => $value) {
            if (str_starts_with($key, 'sales_') || str_starts_with($key, 'target_')) {
                $monthlyTotals[$key] = ($monthlyTotals[$key] ?? 0) + $value;
            }
        }
    }

    /**
     * Calculate totals row (vertical totals across all divisions)
     */
    private function calculateTotalsRow(array $monthlyTotals): array
    {
        $totalsRow = [
            'division_id' => ' ',
            'division_name' => 'TOTAL',
            'division_type_name' => 'ALL',
        ];

        $grandTotalSales = 0;
        $grandTotalTarget = 0;

        // Add monthly totals and calculate achievements
        foreach ($monthlyTotals as $key => $value) {
            $totalsRow[$key] = round($value, 2);

            // Calculate achievement for each month in totals
            if (str_starts_with($key, 'sales_')) {
                $monthKey = str_replace('sales_', '', $key);
                $salesTotal = $value;
                $targetTotal = $monthlyTotals["target_$monthKey"] ?? 0;
                $achievement = $targetTotal > 0 ? ($salesTotal / $targetTotal) * 100 : 0;
                $totalsRow["achievement_$monthKey"] = round($achievement, 2);

                $grandTotalSales += $salesTotal;
            }

            if (str_starts_with($key, 'target_')) {
                $grandTotalTarget += $value;
            }
        }

        // Add grand totals (horizontal totals for the totals row)
        $grandAchievement = $grandTotalTarget > 0 ? ($grandTotalSales / $grandTotalTarget) * 100 : 0;
        $totalsRow['total_sales'] = round($grandTotalSales, 2);
        $totalsRow['total_target'] = round($grandTotalTarget, 2);
        $totalsRow['total_achievement'] = round($grandAchievement, 2);

        return $totalsRow;
    }

    /**
     * Add color coding for top 3 achievers based on total achievement
     */
    private function addTop3Colors(Collection $reportData): Collection
    {
        // Define colors for top 3 achievers
        $colors = [
            1 => '#FFD700', // Gold for 1st place
            2 => '#C0C0C0', // Silver for 2nd place
            3 => '#CD7F32', // Bronze for 3rd place
        ];

        $coloredData = collect();

        foreach ($reportData as $index => $row) {
            $position = $index + 1;

            // Add color and rank information for top 3
            if ($position <= 3) {
                $row['rank_color'] = $colors[$position];
                $row['rank_position'] = $position;
                $row['is_top_achiever'] = true;
            } else {
                $row['rank_color'] = null;
                $row['rank_position'] = $position;
                $row['is_top_achiever'] = false;
            }

            $coloredData->push($row);
        }

        return $coloredData;
    }

    /**
     * Generate field definitions for frontend consumption
     */
    private function generateFieldDefinitions(CarbonPeriod $period): array
    {
        $fields = [];

        // Static fields
        $staticFields = [
            'division_id',
            'division_name',
            'division_type_name',
        ];

        $fields = array_merge($fields, $staticFields);

        // Dynamic monthly fields
        foreach ($period as $date) {
            $monthKey = $date->format('Y_m');

            $monthlyFields = [
                "sales_$monthKey",
                "target_$monthKey",
                "achievement_$monthKey",
            ];

            $fields = array_merge($fields, $monthlyFields);
        }

        // Total fields
        $totalFields = [
            'total_sales',
            'total_target',
            'total_achievement',
        ];

        return array_merge($fields, $totalFields);

    }
}
