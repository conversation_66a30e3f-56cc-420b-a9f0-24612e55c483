<?php


namespace App\Services;

use App\Account;
use App\Exceptions\CrmException;
use App\Models\ListType;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ListService
{

    public function getAllList($from, $to, ?array $lines = [], ?array $divisions = [], ?array $bricks = [], ?array $specialities = [], ?array $accountTypes = [])
    {
        $settingFavourite = true;
        $setting = ListType::first()->type == 'Default List' ? true : false;
        if (!$setting) {
            $settingFavourite = ListType::first()->favourite_type == 'Doctor' ? true : false;
        }
        $list = collect([]);
        if ($setting || $settingFavourite) {
            $list = Account::select(
                'accounts.id as id',
                'accounts.id as account_id',
                'lines.name as line',
                'lines.id as line_id',
                'line_divisions.name as division',
                DB::raw('IFNULL(group_concat(distinct crm_users.fullname),"") as emp'),
                'line_divisions.id as div_id',
                'account_lines.ll as lat',
                'account_lines.lg as lng',
                DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
                DB::raw('IFNULL(group_concat(distinct crm_bricks.id),"") as brick_id'),
                DB::raw('IFNULL(crm_accounts.code,"") as acc_code'),
                DB::raw('IFNULL(crm_accounts.name,"") as account'),
                DB::raw('IFNULL(crm_accounts.notes,"") as notes'),
                DB::raw('IFNULL(crm_accounts.mobile,"") as acc_mobile'),
                DB::raw('IFNULL(crm_doctors.mobile,"") as doc_mobile'),
                DB::raw('IFNULL(crm_accounts.tel,"") as tel'),
                DB::raw('IFNULL(crm_accounts.email,"") as email'),
                DB::raw('IFNULL(crm_accounts.address,"") as address'),
                DB::raw('IFNULL(crm_account_classifications.name,"") as classification'),
                DB::raw('IFNULL(crm_a.name,"") as acc_class'),
                'account_types.name as account_type',
                DB::raw('IFNULL(crm_doctors.name,"") as doctor'),
                DB::raw('IFNULL(crm_doctors.ucode,"") as ucode'),
                DB::raw('IFNULL(crm_doctors.id,"") as doctor_id'),
                DB::raw('IFNULL(crm_d.name,"") as doc_class'),
                'specialities.name as speciality',
                'specialities.id as speciality_id',
                DB::raw('IFNULL(DATE_FORMAT(crm_new_account_doctors.from_date,"%d-%m-%Y"),"") as from_date'),
                DB::raw('IFNULL(DATE_FORMAT(crm_new_account_doctors.to_date,"%d-%m-%Y"),"") as to_date'),
                // DB::raw('Count(distinct crm_actual_visits.id) as no_visits'),
                // DB::raw('Count(distinct crm_planned_visits.id) as no_plans'),
                'division_types.color'
            )
                ->leftJoin('account_types', 'accounts.type_id', 'account_types.id')
                ->leftJoin('account_classifications', 'accounts.classification_id', 'account_classifications.id');
            if (!$setting) {
                $list = $list
                    ->leftJoin('account_lines', function ($join) use ($lines, $divisions) {
                        $join->on('accounts.id', 'account_lines.account_id');
                    })
                    ->leftJoin(
                        'new_account_doctors',
                        function ($join) {
                            $join->on('accounts.id', '=', 'new_account_doctors.account_id');
                            $join->on('account_lines.id', '=', 'new_account_doctors.account_lines_id');
                        }
                    );
            } else {
                $list = $list->leftJoin('account_lines', 'accounts.id', 'account_lines.account_id')
                    ->leftJoin('new_account_doctors', 'accounts.id', 'new_account_doctors.account_id');
            }
            $list = $list->leftJoin('doctors', 'new_account_doctors.doctor_id', 'doctors.id')
                ->leftJoin('lines', 'account_lines.line_id', 'lines.id')
                ->leftJoin('line_divisions', 'account_lines.line_division_id', 'line_divisions.id')
                ->leftJoin(
                    'line_users_divisions',
                    function ($join) {
                        $join->on('line_divisions.id', '=', 'line_users_divisions.line_division_id')
                            ->where('line_users_divisions.from_date', '<=', Carbon::now())
                            ->where(fn($q) => $q->where('line_users_divisions.to_date', '=', null)->orWhere('line_users_divisions.to_date', '>=', (string)Carbon::now()));
                    }
                )
                ->leftJoin('users', 'line_users_divisions.user_id', 'users.id')
                // ->leftJoin('planned_visits', 'accounts.id', 'planned_visits.account_id')
                // ->leftJoin('actual_visits', 'accounts.id', 'actual_visits.account_id')
                ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
                ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
                ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
                ->leftJoin('classes as a', 'account_lines.class_id', 'a.id')
                ->leftJoin('classes as d', 'doctors.class_id', 'd.id')
                ->whereNull('new_account_doctors.deleted_at')
                ->whereNull('account_lines.deleted_at')
                ->whereNull('doctors.deleted_at')
                ->orderBy('doctors.ucode', 'asc');
            if (!empty($bricks)) {
                $list = $list->whereIntegerInRaw('bricks.id', $bricks);
            }

            if (!empty($lines)) {
                $list = $list->whereIntegerInRaw('new_account_doctors.line_id', $lines)
                    ->whereIntegerInRaw('account_lines.line_id', $lines);
            }
            if (!empty($divisions)) {
                $list = $list->whereIntegerInRaw('line_divisions.id', $divisions);
            }
            if (!empty($accountTypes)) {
                $list = $list->whereIntegerInRaw('accounts.type_id', $accountTypes);
            }
            if (!empty($specialities)) {
                $list = $list->whereIntegerInRaw('specialities.id', $specialities);
            }
            $list = $list->groupBy(
                "accounts.id",
                "doctors.id",
                "lines.id",
                "line_divisions.id",
                "a.name",
                "account_lines.ll",
                "account_lines.lg",
                "new_account_doctors.id",
                "new_account_doctors.from_date",
                "new_account_doctors.to_date",
            )->get();
        } else {
            $list = DB::table('accounts')->select(
                'accounts.id as id',
                'accounts.id as account_id',
                'lines.name as line',
                'lines.id as line_id',
                'line_divisions.name as division',
                DB::raw('IFNULL(group_concat(distinct crm_users.fullname),"") as emp'),
                'line_divisions.id as div_id',
                'account_lines.ll as lat',
                'account_lines.lg as lng',
                DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
                DB::raw('IFNULL(group_concat(distinct crm_bricks.id),"") as brick_id'),
                DB::raw('IFNULL(crm_accounts.code,"") as acc_code'),
                DB::raw('IFNULL(crm_accounts.name,"") as account'),
                DB::raw('IFNULL(crm_accounts.notes,"") as notes'),
                DB::raw('IFNULL(crm_accounts.tel,"") as tel'),
                DB::raw('IFNULL(crm_accounts.email,"") as email'),
                DB::raw('IFNULL(crm_accounts.address,"") as address'),
                DB::raw('IFNULL(crm_account_classifications.name,"") as classification'),
                DB::raw('IFNULL(crm_a.name,"") as acc_class'),
                DB::raw('IFNULL(crm_accounts.mobile,"") as acc_mobile'),
                // DB::raw('IFNULL(crm_doctors.mobile,"") as doc_mobile'),
                'account_types.name as account_type',
                DB::raw('Count(distinct crm_actual_visits.id) as no_visits'),
                DB::raw('Count(distinct crm_planned_visits.id) as no_plans'),
                DB::raw('IFNULL(DATE_FORMAT(crm_account_lines.from_date,"%d-%m-%Y"),"") as from_date'),
                DB::raw('IFNULL(DATE_FORMAT(crm_account_lines.to_date,"%d-%m-%Y"),"") as to_date'),
                'division_types.color'
            )
                ->leftJoin('account_types', 'accounts.type_id', 'account_types.id')
                ->leftJoin('account_classifications', 'accounts.classification_id', 'account_classifications.id');

            $list = $list
                ->join('account_lines', function ($join) use ($lines, $divisions) {
                    $join->on('accounts.id', 'account_lines.account_id');
                    if (!empty($lines)) {
                        $join = $join->whereIntegerInRaw('account_lines.line_id', $lines)
                            ->whereIntegerInRaw('account_lines.line_id', $lines);
                    }
                    if (!empty($divisions)) {
                        $join = $join->whereIntegerInRaw('account_lines.line_division_id', $divisions);
                    }
                })
                ->leftJoin('lines', 'account_lines.line_id', 'lines.id')
                ->leftJoin('line_divisions', 'account_lines.line_division_id', 'line_divisions.id')
                ->leftJoin(
                    'line_users_divisions',
                    function ($join) {
                        $join->on('line_divisions.id', '=', 'line_users_divisions.line_division_id')
                            ->where('line_users_divisions.from_date', '<=', Carbon::now())
                            ->where(fn($q) => $q->where('line_users_divisions.to_date', '=', null)->orWhere('line_users_divisions.to_date', '>=', (string)Carbon::now()));
                    }
                )->leftJoin('users', 'line_users_divisions.user_id', 'users.id')
                ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
                ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
                ->leftJoin('planned_visits', 'accounts.id', 'planned_visits.account_id')
                ->leftJoin('actual_visits', 'accounts.id', 'actual_visits.account_id')
                ->leftJoin('classes as a', 'account_lines.class_id', 'a.id')
                ->whereNull('account_lines.deleted_at')
                ->orderBy('accounts.code', 'asc');
            if (!empty($bricks)) {
                $list = $list->whereIntegerInRaw('bricks.id', $bricks);
            }

            if (!empty($lines)) {
                $list = $list->whereIntegerInRaw('account_lines.line_id', $lines);
            }
            if (!empty($divisions)) {
                $list = $list->whereIntegerInRaw('line_divisions.id', $divisions);
            }
            if (!empty($accountTypes)) {
                $list = $list->whereIntegerInRaw('accounts.type_id', $accountTypes);
            }
            $list = $list->groupBy(
                "accounts.id",
                "lines.id",
                "line_divisions.id",
                "a.name",
                "account_lines.ll",
                "account_lines.lg",
                "account_lines.from_date",
                "account_lines.to_date",
            )->get();
        }

        return $list;
    }


    public function getMasterList(?array $lines = [], ?array $divisions = [], ?array $bricks = [], ?array $specialities = [], ?array $accountTypes = [])
    {
        $setting = ListType::first()->type == 'Default List' ? true : false;
        $list = Account::select(
            'accounts.id as id',
            'accounts.id as account_id',
            DB::raw('IFNULL(group_concat(distinct crm_lines.id),"") as line_id'),
            DB::raw('IFNULL(group_concat(distinct crm_lines.name),"") as line'),
            DB::raw('IFNULL(group_concat(distinct crm_line_divisions.id),"") as div_id'),
            DB::raw('IFNULL(group_concat(distinct crm_line_divisions.name),"") as division'),
            DB::raw('IFNULL(group_concat(distinct crm_division_types.color),"") as color'),
            DB::raw('IFNULL(group_concat(distinct crm_users.fullname),"") as emp'),
            DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
            DB::raw('IFNULL(group_concat(distinct crm_bricks.id),"") as brick_id'),
            DB::raw('IFNULL(crm_accounts.code,"") as acc_code'),
            DB::raw('IFNULL(crm_accounts.name,"") as account'),
            DB::raw('IFNULL(crm_accounts.notes,"") as notes'),
            DB::raw('IFNULL(crm_accounts.mobile,"") as acc_mobile'),
            // DB::raw('IFNULL(crm_doctors.mobile,"") as doc_mobile'),
            DB::raw('IFNULL(crm_accounts.tel,"") as tel'),
            DB::raw('IFNULL(crm_accounts.email,"") as email'),
            DB::raw('IFNULL(crm_accounts.address,"") as address'),
            DB::raw('IFNULL(crm_account_classifications.name,"") as classification'),
            // DB::raw('IFNULL(crm_a.name,"") as acc_class'),
            DB::raw('IFNULL(group_concat(distinct crm_a.name),"") as acc_class'),
            'account_types.name as account_type',
            DB::raw('IFNULL(group_concat(distinct crm_doctors.id),"") as doctor_id'),
            DB::raw('IFNULL(group_concat(distinct crm_doctors.name),"") as doctor'),
            DB::raw('IFNULL(group_concat(distinct crm_doctors.ucode),"") as ucode'),
            DB::raw('IFNULL(group_concat(distinct crm_specialities.id),"") as speciality_id'),
            DB::raw('IFNULL(group_concat(distinct crm_specialities.name),"") as speciality'),
            // DB::raw('IFNULL(crm_doctors.name,"") as doctor'),
            // DB::raw('IFNULL(crm_doctors.ucode,"") as ucode'),
            // DB::raw('IFNULL(crm_doctors.id,"") as doctor_id'),
            // DB::raw('IFNULL(crm_d.name,"") as doc_class'),
            // 'specialities.name as speciality',
            // 'specialities.id as speciality_id',
        )
            ->join('account_types', function ($join) use ($accountTypes) {
                $join->on('accounts.type_id', 'account_types.id');
                if (!empty($accountTypes)) {
                    $join->whereIntegerInRaw('account_types.id', $accountTypes);
                }
            })
            ->leftJoin('account_classifications', 'accounts.classification_id', 'account_classifications.id');
        if (!$setting) {
            $list = $list
                ->join('account_lines', function ($join) {
                    $join->on('accounts.id', 'account_lines.account_id')
                        ->whereNull('account_lines.deleted_at');
                })
                ->join(
                    'new_account_doctors',
                    function ($join) {
                        $join->on('accounts.id', '=', 'new_account_doctors.account_id');
                        $join->on('account_lines.id', '=', 'new_account_doctors.account_lines_id')
                            ->whereNull('new_account_doctors.deleted_at');
                    }
                );
        } else {
            $list = $list->join('account_lines', 'accounts.id', 'account_lines.account_id')
                ->join('new_account_doctors', 'accounts.id', 'new_account_doctors.account_id')
                ->whereNull('account_lines.deleted_at')
                ->whereNull('new_account_doctors.deleted_at');
        }
        $list = $list->join('doctors', function ($join) use ($specialities) {
            $join->on('new_account_doctors.doctor_id', 'doctors.id')
                ->whereNull('doctors.deleted_at');
            if (!empty($specialities)) {
                $join->whereIntegerInRaw('doctors.speciality_id', $specialities);
            }
        })
            ->leftJoin('lines', 'account_lines.line_id', 'lines.id')
            ->leftJoin('line_divisions', 'account_lines.line_division_id', 'line_divisions.id')
            ->leftJoin(
                'line_users_divisions',
                function ($join) {
                    $join->on('line_divisions.id', '=', 'line_users_divisions.line_division_id')
                        ->where('line_users_divisions.from_date', '<=', Carbon::now())
                        ->where(fn($q) => $q->where('line_users_divisions.to_date', '=', null)->orWhere('line_users_divisions.to_date', '>=', (string)Carbon::now()));
                }
            )
            ->leftJoin('users', 'line_users_divisions.user_id', 'users.id')
            ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('classes as a', 'account_lines.class_id', 'a.id')
            // ->leftJoin('classes as d', 'doctors.class_id', 'd.id')
            // ->whereYear('accounts.active_date', 2025)
            // ->whereNull('new_account_doctors.deleted_at')
            ->whereNull('accounts.deleted_at')
            ->whereNull('lines.deleted_at')
            ->whereNull('line_divisions.deleted_at')

            ->whereNull('doctors.deleted_at')
            ->where('accounts.hidden_fav_list', 0)
            ->orderBy('accounts.id', 'asc')->groupBy(
                "accounts.id",
            )->get();
        return $list;
    }
    public function getActiveList(?Carbon $from = null, ?Carbon $to = null, ?array $lines = [], ?array $divisions = [], ?array $bricks = [], ?array $specialities = [], ?array $accountTypes = [], ?array $accountDoctorIds = [])
    {
        $now = Carbon::now()->toDateString();
        $setting = ListType::first()->type == 'Default List' ? true : false;
        $list = Account::select(
            // 'accounts.id as id',
            'doctors.id as id',
            'accounts.id as account_id',
            'lines.name as line',
            'lines.id as line_id',
            'line_divisions.name as division',
            DB::raw('IFNULL(group_concat(distinct crm_users.fullname),"") as emp'),
            'line_divisions.id as div_id',
            'account_lines.ll as lat',
            'account_lines.lg as lng',
            DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
            DB::raw('IFNULL(group_concat(distinct crm_bricks.id),"") as brick_id'),
            DB::raw('IFNULL(crm_accounts.code,"") as acc_code'),
            DB::raw('IFNULL(crm_accounts.name,"") as account'),
            DB::raw('IFNULL(crm_accounts.notes,"") as notes'),
            DB::raw('IFNULL(crm_accounts.mobile,"") as acc_mobile'),
            DB::raw('IFNULL(crm_accounts.tel,"") as tel'),
            DB::raw('IFNULL(crm_accounts.email,"") as email'),
            DB::raw('IFNULL(crm_accounts.address,"") as address'),
            DB::raw('IFNULL(crm_account_classifications.name,"") as classification'),
            DB::raw('IFNULL(crm_a.name,"") as acc_class'),
            DB::raw("CONCAT(crm_accounts.name ,'(', crm_doctors.name , ')') as account_doctor_name"),
            DB::raw("CONCAT(crm_accounts.id ,'_', crm_doctors.id) as account_dr_ids"),
            'account_types.name as account_type',
            DB::raw('IFNULL(crm_doctors.name,"") as doctor'),
            DB::raw('IFNULL(crm_doctors.mobile,"") as doc_mobile'),
            DB::raw('IFNULL(crm_doctors.ucode,"") as ucode'),
            DB::raw('IFNULL(crm_doctors.id,"") as doctor_id'),
            DB::raw('IFNULL(crm_d.name,"") as doc_class'),
            'specialities.name as speciality',
            'new_account_doctors.id as acc_dr_id',
            DB::raw('DATE_FORMAT(crm_new_account_doctors.from_date,"%d-%m-%Y") as from_date'),
            DB::raw('IFNULL(DATE_FORMAT(crm_new_account_doctors.to_date,"%d-%m-%Y"),"") as to_date'),
            'division_types.color'
        )
            ->addSelect(DB::raw("null as reason_id"))
            ->leftJoin('account_classifications', 'accounts.classification_id', 'account_classifications.id')
            ->where('accounts.deleted_at', null)
            ->where('doctors.deleted_at', null)
            ->where(fn($q) => $q->where('accounts.inactive_date', '=', null)->orWhere('accounts.inactive_date', '>=', $now))
            ->where(fn($q) => $q->where('doctors.inactive_date', '=', null)->orWhere('doctors.inactive_date', '>=', $now))
            ->where('accounts.active_date', '<=', $now)
            ->where('doctors.active_date', '<=', $now)
            ->join('account_types', function ($join) use ($accountTypes) {
                $join->on('accounts.type_id', 'account_types.id');
                if (!empty($accountTypes)) {
                    $join->whereIntegerInRaw('account_types.id', $accountTypes);
                }
            })
            ->join('account_lines', function ($join) use ($lines, $divisions, $bricks, $from, $to, $now) {
                $join->on('accounts.id', 'account_lines.account_id')
                    ->whereIntegerInRaw('account_lines.line_id', $lines)
                    ->whereNull('account_lines.deleted_at')
                    ->where('account_lines.from_date', '<=', $from?->toDateString() ?? $now)
                    ->where(fn($q) => $q->where('account_lines.to_date', '=', null)
                        ->orWhere('account_lines.to_date', '>=', $to?->toDateString() ?? $now))
                    ->whereIntegerInRaw('account_lines.line_division_id', $divisions);
                if (!empty($bricks)) {
                    $join->whereIntegerInRaw('account_lines.brick_id', $bricks);
                }
            });
        if (!$setting) {
            $list = $list
                ->join(
                    'new_account_doctors',
                    function ($join) use ($lines, $from, $to, $now) {
                        $join->on('accounts.id', '=', 'new_account_doctors.account_id');
                        $join->on('account_lines.id', '=', 'new_account_doctors.account_lines_id')
                            ->whereIntegerInRaw('new_account_doctors.line_id', $lines)
                            ->whereNull('new_account_doctors.deleted_at')
                            ->where('new_account_doctors.from_date', '<=',  $from?->toDateString() ?? $now)
                            ->where(fn($q) => $q->where('new_account_doctors.to_date', '=', null)
                                ->orWhere('new_account_doctors.to_date', '>=', $to?->toDateString() ?? $now));
                    }
                );
        } else {
            $list = $list
                ->join('new_account_doctors', function ($join) use ($lines, $now, $from, $to, $accountDoctorIds) {
                    $join->on('accounts.id', 'new_account_doctors.account_id')
                        ->whereIntegerInRaw('new_account_doctors.line_id', $lines)
                        ->whereNull('new_account_doctors.deleted_at')
                        ->where('new_account_doctors.from_date', '<=',  $from?->toDateString() ?? $now)
                        ->where(fn($q) => $q->where('new_account_doctors.to_date', '=', null)
                            ->orWhere('new_account_doctors.to_date', '>=', $to?->toDateString() ?? $now));
                    if (!empty($accountDoctorIds)) {
                        $join->whereIntegerInRaw('new_account_doctors.id', $accountDoctorIds);
                    }
                });
        }
        $list = $list->join('doctors', function ($join) use ($specialities) {
            $join->on('new_account_doctors.doctor_id', 'doctors.id')
                ->whereNull('doctors.deleted_at');
            if (!empty($specialities)) {
                $join->whereIntegerInRaw('doctors.speciality_id', $specialities);
            }
        })
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('lines', 'account_lines.line_id', 'lines.id')
            ->leftJoin('line_divisions', 'account_lines.line_division_id', 'line_divisions.id')
            ->leftJoin(
                'line_users_divisions',
                function ($join) use ($now) {
                    $join->on('line_divisions.id', '=', 'line_users_divisions.line_division_id')
                        ->whereNull('line_users_divisions.deleted_at')
                        ->where('line_users_divisions.from_date', '<=', $now)
                        ->where(fn($q) => $q->where('line_users_divisions.to_date', '=', null)->orWhere('line_users_divisions.to_date', '>=', $now));
                }
            )
            ->leftJoin('users', 'line_users_divisions.user_id', 'users.id')
            ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
            ->leftJoin('classes as a', 'account_lines.class_id', 'a.id')
            ->leftJoin('classes as d', 'doctors.class_id', 'd.id')
            ->orderBy('doctors.ucode', 'asc');

        $list = $list->groupBy(
            "accounts.id",
            "account_types.name",
            "doctors.id",
            "lines.id",
            "line_divisions.id",
            "a.name",
            "account_lines.ll",
            "account_lines.lg",
            "new_account_doctors.id",
            "new_account_doctors.from_date",
            "new_account_doctors.to_date",
        )->get();
        return $list;
    }
    public function getInactiveList($from, $to, ?array $lines = [], ?array $divisions = [], ?array $bricks = [], ?array $specialities = [], ?array $accountTypes = [], ?array $accountDoctorIds = [])
    {
        $list = Account::select(
            'accounts.id as account_id',
            'accounts.id as id',
            'lines.name as line',
            'lines.id as line_id',
            'line_divisions.name as division',
            'line_divisions.id as div_id',
            'account_lines.ll as lat',
            'account_lines.lg as lng',
            DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
            DB::raw('IFNULL(group_concat(distinct crm_bricks.id),"") as brick_id'),
            DB::raw('IFNULL(crm_accounts.code,"") as acc_code'),
            DB::raw('IFNULL(crm_accounts.name,"") as account'),
            DB::raw('IFNULL(crm_accounts.notes,"") as notes'),
            DB::raw('IFNULL(crm_accounts.tel,"") as tel'),
            DB::raw('IFNULL(crm_accounts.email,"") as email'),
            DB::raw('IFNULL(crm_accounts.address,"") as address'),
            DB::raw('IFNULL(crm_account_classifications.name,"") as classification'),
            DB::raw('IFNULL(crm_a.name,"") as acc_class'),
            'account_types.name as account_type',
            DB::raw('IFNULL(crm_doctors.name,"") as doctor'),
            DB::raw('IFNULL(crm_doctors.ucode,"") as ucode'),
            DB::raw('IFNULL(crm_doctors.id,"") as doctor_id'),
            DB::raw('IFNULL(crm_d.name,"") as doc_class'),
            'specialities.name as speciality',
            'specialities.id as speciality_id',
            'new_account_doctors.id as acc_dr_id',
            DB::raw('DATE_FORMAT(crm_new_account_doctors.from_date,"%d-%m-%Y") as from_date'),
            DB::raw('IFNULL(DATE_FORMAT(crm_new_account_doctors.to_date,"%d-%m-%Y"),"") as to_date'),
            'division_types.color'
        )
            ->addSelect(DB::raw("null as reason_id"))
            // ->addSelect(DB::raw("App\Models\NewAccountDoctor as visitable_type"))
            ->leftJoin('account_types', 'accounts.type_id', 'account_types.id')
            ->leftJoin('account_classifications', 'accounts.classification_id', 'account_classifications.id')
            ->leftJoin('account_lines', 'accounts.id', 'account_lines.account_id')
            // ->leftJoin('new_account_doctors', function ($join) {
            //     $join->on('accounts.id', 'new_account_doctors.account_id')
            //         ->whereIn('new_account_doctors.id', function ($q) {
            //             $q->selectRaw("MAX(crm_new_account_doctors.id)")
            //                 ->from("new_account_doctors")
            //                 ->leftJoin("accounts", "new_account_doctors.account_id", "accounts.id")
            //                 ->groupBy("accounts.id");
            //         });
            // })
            ->leftJoin('new_account_doctors', 'accounts.id', 'new_account_doctors.account_id')
            ->leftJoin('lines', 'account_lines.line_id', 'lines.id')
            ->leftJoin('line_divisions', 'account_lines.line_division_id', 'line_divisions.id')
            ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
            ->leftJoin('doctors', function ($join) {
                $join->on('new_account_doctors.doctor_id', 'doctors.id')
                    ->where('new_account_doctors.deleted_at', null)
                    ->whereIn('new_account_doctors.id', function ($q) {
                        $q->selectRaw("MAX(crm_new_account_doctors.id)")
                            ->from("new_account_doctors")
                            ->leftJoin("doctors", "new_account_doctors.doctor_id", "doctors.id")
                            ->groupBy("doctors.id");
                    });
            })
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('classes as d', 'doctors.class_id', 'd.id')
            ->leftJoin('classes as a', 'account_lines.class_id', 'a.id')
            ->where(fn($q) => $q->where('new_account_doctors.from_date', '>', (string)Carbon::now())
                ->orWhere('new_account_doctors.to_date', '<', (string)Carbon::now()))
            ->where('accounts.deleted_at', null)
            ->where('account_lines.deleted_at', null)
            ->where('doctors.deleted_at', null);
        if (!empty($bricks)) {
            $list = $list->whereIntegerInRaw('bricks.id', $bricks);
        }

        if (!empty($lines)) {
            $list = $list->whereIntegerInRaw('new_account_doctors.line_id', $lines)
                ->whereIntegerInRaw('account_lines.line_id', $lines);
        }
        if (!empty($divisions)) {
            $list = $list->whereIntegerInRaw('line_divisions.id', $divisions);
        }
        if (!empty($accountTypes)) {
            $list = $list->whereIntegerInRaw('accounts.type_id', $accountTypes);
        }
        if (!empty($specialities)) {
            $list = $list->whereIntegerInRaw('specialities.id', $specialities);
        }
        if (!empty($accountDoctorIds)) {
            $list = $list->whereIntegerInRaw('new_account_doctors.id', $accountDoctorIds);
        }
        $list = $list
            ->groupBy(
                "accounts.id",
                "doctors.id",
                "lines.id",
                "line_divisions.id",
                "a.name",
                "account_lines.ll",
                "account_lines.lg",
                'new_account_doctors.id',
                "new_account_doctors.from_date",
                "new_account_doctors.to_date",
            )
            ->get();
        // ->unique(function ($item) {
        //  return $item['id'] . $item['doctor_id'] . $item['line_id'] . $item['div_id'];
        // })
        // ->values();
        return $list;
    }
}
