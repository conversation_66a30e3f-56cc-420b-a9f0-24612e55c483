<?php

namespace App\Services;

use App\ApprovablePlanable;
use App\DivisionType;
use App\Models\ApprovalFlowUser;
use App\Models\ApprovalSetting;
use App\Models\PV\PV;
use App\Position;
use App\Vacation;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class ApprovalTimeService
{
    public function handleApprovalTime()
    {
        $approvalSetting = ApprovalSetting::where('key', 'vacation_approval_center_flow')->value('value');
        Log::info('approvalSetting: ' . $approvalSetting);
        $rawData = $this->allApprovables();
        Log::info('rawData: ' . $rawData);
        $users = $this->getUsersData($rawData);
        Log::info('users: ' . $users);
        return $this->checkApprovals($users, $approvalSetting);
    }

    private function allApprovables()
    {
        return ApprovablePlanable::with([
            'approvable.approvable' => function ($morphTo) {
                $morphTo->morphWith([
                    \App\Position::class => ['users:id,fullname'],
                    \App\DivisionType::class => ['divisions.users:id,fullname'],
                ]);
            }
        ])
            ->where('request_type', Vacation::class)
            ->where('num_days', '>', 0)
            ->where('required', 0)
            ->get();
    }

    private function getUsersData($rawData)
    {
        $userDays = collect();

        foreach ($rawData as $item) {
            $approvable = $item->approvable?->approvable;

            if (!$approvable) continue;

            $users = $approvable instanceof \App\Position
                ? ($approvable->users ?? collect())
                : ($approvable instanceof \App\DivisionType
                    ? $approvable->divisions->flatMap(fn($d) => $d->users)
                    : collect());
            foreach ($users as $user) {
                $existing = $userDays->get($user->id);
                if (!$existing) {
                    // Clone the user and attach a custom property (avoid overriding core model attributes)
                    $user->days = $item->num_days;
                    $userDays->put($user->id, $user);
                } else {
                    $existing->days += $item->num_days;
                }
            }
        }

        return $userDays->sortByDesc('days')->values();
    }

    private function checkApprovals($users, $approvalSetting)
    {
        $today = now()->toDateString();
        foreach ($users as $user) {
            $vacations = collect([]);
            $lines = $user->lines;
            $usersObjIds = $this->getUsersObjects($user, $lines);
            $vacations =  Vacation::whereIntegerInRaw('user_id', [2628])
                // whereIntegerInRaw('user_id', $usersObjIds)
                ->whereHas('details', function ($q) {
                    $q->whereNull('approval');
                })
                ->whereDoesntHave('approvalFlows', function ($query) use ($user) {
                    $query->where('approval_flow_users.user_id', $user->id);
                })->with(['approvalFlows'])->get()->filter(function ($vacation) use ($today, $user) {
                    $latestApprovalFlow = $vacation->approvalFlows->sortByDesc('created_at')->first();

                    $baseDate = $latestApprovalFlow
                        ? $latestApprovalFlow->created_at
                        : $vacation->created_at;

                    return $baseDate
                        ->copy()
                        ->addDays($user->days)
                        ->toDateString() <= $today;
                })
                ->values();


            $vacations = $this->checkApproval($vacations, $user, $lines, $approvalSetting);
            Log::info('user: ' . $user);
            Log::info('vacations : ' . $vacations);

            foreach ($vacations as $vacation) {
                ApprovalFlowUser::firstOrCreate([
                    'detail_id' => $vacation->details->id,
                    'user_id' => $user->id,
                    'approval' => 1,
                    'description' => 'Auto Approved'
                ]);
            }
        }
    }
    private function getUsersObjects($user, $lines)
    {
        $usersObj = collect([]);
        $types = [DivisionType::class, Position::class];
        foreach ($lines as $line) {
            foreach ($types as $type) {
                $usersObj = $usersObj->merge($user->planableUsers($line->id, Vacation::class, $type));
            }
        }
        return $usersObj->unique('id')->values()->pluck('id');
    }

    private function checkApproval($vacations, $user, $lines, $approvalSetting)
    {
        $dataFlow = '';
        $scanLevel = 1;
        $vacations = $vacations->filter(
            function (Vacation $vacation) use ($lines, $approvalSetting, $scanLevel, $user, $dataFlow) {
                $from = Carbon::parse($vacation->from_date)->startOfMonth();
                $to = Carbon::parse($vacation->to_date)->endOfMonth();
                if ($approvalSetting == 'Yes' && isNullable($vacation->details?->approval)) {
                    $approvalData = $user->userApprovals($from, $to);
                    $linesAapprovables = $approvalData['linesAapprovables'];
                    $approvablesCountOnThisShit = $vacation->details?->approvalFlows()->count();
                    $data = $user->approvalWidget($vacation, $user, Vacation::class, $from, $to, $lines, $linesAapprovables);
                    $dataFlow = $data['linesDataFlow'];
                    $currentFlow = $dataFlow?->flow;
                    $vacantCount = $data['vacantCount'];
                    $haveToApprove = $approvablesCountOnThisShit === $currentFlow - ($vacantCount + $scanLevel);
                    return $haveToApprove;
                }
            }
        );
        return $vacations->values();
    }
}
