<?php

namespace App;

use App\Exceptions\CrmException;
use App\Models\Attachment;
use App\Models\OffDay;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\ModelExportable;
use App\Traits\PdfExportable;
use App\Traits\SendMail;
use Carbon\CarbonPeriod;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Staudenmeir\EloquentHasManyDeep\HasRelationships;

class Vacation extends Model
{
    use SoftDeletes;
    use ModelExportable;
    use PdfExportable;
    use SendMail;
    use HasRelationships;

    protected $guard_name = 'api';

    protected $table = 'vacations';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'from_date',
        'to_date',
        'shift_id',
        'notes',
        'vacation_type_id',
        'user_id',
        'full_day',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'from_date' => 'datetime',
        'to_date' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function type()
    {
        return $this->belongsTo(VacationType::class, 'vacation_type_id');
    }
    public function attachments()
    {
        return $this->morphMany(Attachment::class, 'attachable');
    }
    public function shift()
    {
        return $this->belongsTo(Shift::class);
    }

    public function details()
    {
        return $this->morphOne(PlanVisitDetails::class, 'visitable');
    }
    public function approvalFlows()
    {
        return $this->hasManyDeepFromRelations(
            $this->details(),
            (new PlanVisitDetails)->approvalFlows()
        );
    }
    public function reasons()
    {
        return $this->morphToMany(Reason::class, 'reasonable');
    }
    public function restore()
    {
        $this->withTrashed()->where('id', $this->id)->restore();
        $this->attachments()->withTrashed()->where('attachable_id', $this->id)
            ->where('attachable_type', Vacation::class)->restore();
    }
    public function forceDelete()
    {
        $this->attachments()->withTrashed()->where('attachable_id', $this->id)
            ->where('attachable_type', Vacation::class)->forceDelete();
        Vacation::withTrashed()->where('id', $this->id)->forceDelete();
    }
    public static function calendar(User $user)
    {
        $color = "#F70EB5";
        return DB::table('vacations')->select(
            'vacations.user_id',
            'vacation_types.name as type',
            'shifts.name as shift',
            'vacations.from_date',
            'vacations.to_date',
        )
            ->leftJoin('shifts', 'vacations.shift_id', 'shifts.id')
            ->leftJoin('vacation_types', 'vacations.vacation_type_id', 'vacation_types.id')
            ->whereNull('vacations.deleted_at')
            ->where('vacations.user_id', $user->id)->get()->map(function ($item) use ($color) {
                return [
                    'color' => $color,
                    'start' => Carbon::parse($item->from_date)->toDateString(),
                    'end' => Carbon::parse($item->to_date)->toDateString(),
                    'name' => $item->type,
                    'category' => $item->shift ?? 'Full-Day',
                    'timed' => true
                ];
            });
    }

    static public function getVacationsDatePerPeriod(User $user, $from, $to, array $month, string $year, array $shifts, ?array $types = [], $line_id = null)
    {
        $vacations = Vacation::select('id', 'user_id', 'from_date', 'to_date', 'full_day', 'shift_id')
            ->where('user_id', $user->id)
            ->where(fn($q) => $q->whereIntegerInRaw('shift_id', $shifts)->orWhereNull('shift_id'))
            ->where(fn($q) => $q->whereBetween(DB::raw("(DATE_FORMAT(crm_vacations.from_date,'%m'))"), $month)
                ->orWhereBetween(DB::raw("(DATE_FORMAT(crm_vacations.from_date,'%Y-%m'))"), [Carbon::parse($from)->addYear(-1)->format('Y-m'), Carbon::parse($to)->format('Y-m')]))
            ->where(fn($q) => $q->whereYear('vacations.from_date', $year)->orWhereYear('vacations.to_date', $year))
            ->whereHas('details', function ($q) {
                $q->where('approval', 1);
            });
        if (!empty($types)) $vacations = $vacations->whereIntegerInRaw('vacation_type_id', $types);

        $vacations = $vacations->get();
        $count = 0.0;
        $shifts = count($shifts) > 1 ? [1, 2] : $shifts;
        foreach ($vacations as $vacation) {
            $period = CarbonPeriod::create($vacation->from_date, $vacation->to_date);
            foreach ($period as $date) {
                foreach ($shifts as $shift) {
                    if ($date->between($from, $to)) {
                        if (
                            !OffDay::isOffDay($vacation->from_date, $vacation->to_date, $date, $shift, $line_id) &&
                            !PublicHoliday::isPublicHoliday($from, $to, $date, $line_id)
                        ) {
                            count($shifts) > 1 ? $count += 0.5 : $count += 1.0;
                        }
                    }
                }
            }
        }
        return $count;
    }

    static public function getVacationsDatePerPeriodWithoutStatus(User $user, $vacation, array $shifts)
    {
        $count = 0.0;
        // $shifts = empty($shifts) ? [1, 2] : $shifts;
        $period = CarbonPeriod::create($vacation->from_date, $vacation->to_date);
        foreach ($period as $date) {
            foreach ($shifts as $shift) {
                if (
                    !OffDay::isOffDay($vacation->from_date, $vacation->to_date, $date, $shift) &&
                    !PublicHoliday::isPublicHoliday($vacation->from_date, $vacation->to_date, $date)
                ) {
                    $count += 0.5;
                }
            }
        }
        return $count;
    }

    public function feedbacks()
    {
        return $this->morphMany('App\Models\RequestFeedback', 'requestable');
    }
}
