<?php

namespace App\Providers;

use Illuminate\Contracts\Database\Query\Expression;
use Illuminate\Database\Grammar;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\ServiceProvider;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;

class QueryMacroServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $this->whereTupleInSetup();
        $this->orderBySequence();

        $this->unionFromCallback();
    }

    private function orderBySequence(): void
    {
        Builder::macro('orderBySequence', function (string $column, array $ids) {
            return $this->orderByRaw('FIELD(' . $column . ', ' . implode(',', $ids) . ')');
        });
    }

    private function whereTupleInSetup(): void
    {
        Builder::macro('whereTupleCondition', function (array $columns, array $values, string $operator): Builder {
            /** @var Builder $this */

            if (count($columns) < 2) {
                throw new \InvalidArgumentException("Tuple must contain at least two columns.");
            }

            if (empty($values)) {
                return $operator === 'IN'
                    ? $this->whereRaw('1 = 0') // No matches if values are empty for IN
                    : $this; // Ignore if NOT IN has no values
            }

            // Validate that each set of values matches the column count
            foreach ($values as $valueSet) {
                if (!is_array($valueSet) || count($valueSet) !== count($columns)) {
                    throw new \InvalidArgumentException("Each value set must match the number of columns.");
                }
            }

            // Generate placeholders (e.g., "(?, ?), (?, ?), (?, ?)")
            $placeholders = implode(', ', array_fill(0, count($values), '(' . implode(', ', array_fill(0, count($columns), '?')) . ')'));


            $columnTuple = '(' . implode(', ', array_map(function ($col) {
                    if ($col instanceof Expression) {
                        return $col->getValue(DB::connection()->getQueryGrammar());
                    }
                    return "`$col`"; // Regular column with backticks
                }, $columns)) . ')';

            // Flatten the values for binding
            $bindings = array_merge(...$values);

            return $this->whereRaw("$columnTuple $operator ($placeholders)", $bindings);
        });

        Builder::macro('whereTupleIn', function (array $columns, array $values): Builder {
            /** @var Builder $this */
            return $this->whereTupleCondition($columns, $values, 'IN');
        });

        Builder::macro('whereNotTupleIn', function (array $columns, array $values): Builder {
            /** @var Builder $this */
            return $this->whereTupleCondition($columns, $values, 'NOT IN');
        });
    }

    private function unionFromCallback(): void
    {
        $unionFromCallback = function (iterable $items, callable $callback, bool $useUnionAll = true) {
            $unionQuery = null;
            $first = true;

            foreach ($items as $item) {
                $query = $callback($item);

                if ($first) {
                    $unionQuery = $query;
                    $first = false;
                } else {
                    if ($useUnionAll) {
                        $unionQuery->unionAll($query);
                    } else {
                        $unionQuery->union($query);
                    }
                }
            }

            return $unionQuery;
        };

        Builder::macro('unionFromCallback', $unionFromCallback);

        EloquentBuilder::macro('unionFromCallback', $unionFromCallback);
    }
}
